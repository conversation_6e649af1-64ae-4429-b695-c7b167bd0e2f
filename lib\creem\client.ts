// lib/creem/client.ts
// Creem SDK客户端

import { getCreemConfig } from './config';
import type { 
  CreemProduct, 
  CreemCheckoutSession, 
  CreemCustomer, 
  CreemCreateCheckoutParams,
  CreemApiResponse,
  CreemApiError
} from './types';

export class CreemClient {
  private config = getCreemConfig();

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.config.apiUrl}${endpoint}`;

    console.log('Creem API Request:', {
      url,
      method: options.method || 'GET',
      hasApiKey: !!this.config.apiKey,
      apiKeyPrefix: this.config.apiKey?.substring(0, 10) + '...',
      body: options.body
    });

    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    console.log('Creem API Response:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    if (!response.ok) {
      let errorText = '';
      try {
        errorText = await response.text();
        console.log('Creem API Error Response:', errorText);
      } catch (e) {
        console.log('Failed to read error response:', e);
      }

      let error: CreemApiError;
      try {
        error = JSON.parse(errorText);
      } catch (e) {
        error = {
          message: `HTTP ${response.status}: ${response.statusText}. Response: ${errorText}`,
          code: response.status.toString(),
        };
      }

      throw new Error(error.message || `Unknown Creem API error: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.json();
    console.log('Creem API Success Response:', responseData);
    return responseData;
  }

  async createCheckoutSession(params: CreemCreateCheckoutParams): Promise<CreemCheckoutSession> {
    try {
      const response = await this.request<CreemApiResponse<CreemCheckoutSession>>('/checkout/sessions', {
        method: 'POST',
        body: JSON.stringify(params),
      });
      
      return response.data;
    } catch (error) {
      throw new Error(`Failed to create checkout session: ${error}`);
    }
  }

  async retrieveCheckoutSession(sessionId: string): Promise<CreemCheckoutSession> {
    try {
      const response = await this.request<CreemApiResponse<CreemCheckoutSession>>(`/checkout/sessions/${sessionId}`);
      return response.data;
    } catch (error) {
      throw new Error(`Failed to retrieve checkout session: ${error}`);
    }
  }

  async createCustomer(email: string, name?: string): Promise<CreemCustomer> {
    try {
      const response = await this.request<CreemApiResponse<CreemCustomer>>('/customers', {
        method: 'POST',
        body: JSON.stringify({ email, name }),
      });
      
      return response.data;
    } catch (error) {
      throw new Error(`Failed to create customer: ${error}`);
    }
  }

  async retrieveCustomer(customerId: string): Promise<CreemCustomer> {
    try {
      const response = await this.request<CreemApiResponse<CreemCustomer>>(`/customers/${customerId}`);
      return response.data;
    } catch (error) {
      throw new Error(`Failed to retrieve customer: ${error}`);
    }
  }

  async listProducts(): Promise<CreemProduct[]> {
    try {
      const response = await this.request<CreemApiResponse<CreemProduct[]>>('/products');
      return response.data;
    } catch (error) {
      throw new Error(`Failed to list products: ${error}`);
    }
  }

  async retrieveProduct(productId: string): Promise<CreemProduct> {
    try {
      const response = await this.request<CreemApiResponse<CreemProduct>>(`/products/${productId}`);
      return response.data;
    } catch (error) {
      throw new Error(`Failed to retrieve product: ${error}`);
    }
  }

  async createCustomerPortalSession(customerId: string): Promise<{ id: string; url: string }> {
    try {
      const response = await this.request<CreemApiResponse<{ id: string; url: string }>>('/customer-portal/sessions', {
        method: 'POST',
        body: JSON.stringify({ customer_id: customerId }),
      });

      return response.data;
    } catch (error) {
      throw new Error(`Failed to create customer portal session: ${error}`);
    }
  }

  verifyWebhookSignature(payload: string, signature: string): boolean {
    // 实现webhook签名验证逻辑
    // 这里需要根据Creem的实际签名算法实现
    try {
      const crypto = require('crypto');
      const expectedSignature = crypto
        .createHmac('sha256', this.config.webhookSecret)
        .update(payload)
        .digest('hex');

      return signature === expectedSignature;
    } catch (error) {
      console.error('Webhook signature verification failed:', error);
      return false;
    }
  }
}

// 单例模式
let creemClient: CreemClient | null = null;

export function getCreemClient(): CreemClient {
  if (!creemClient) {
    creemClient = new CreemClient();
  }
  return creemClient;
}