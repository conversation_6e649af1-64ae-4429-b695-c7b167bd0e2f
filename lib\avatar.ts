/**
 * 生成用户头像首字母，按优先级顺序：
 * 1. 用户昵称首字母
 * 2. 邮箱用户名首字母 (<EMAIL> 取 "U")
 * 3. 默认 'U'
 */
export function getAvatarInitial(user: { nickname?: string; email?: string } | null | undefined): string {
  if (!user) {
    return 'U';
  }

  // 优先使用昵称首字母
  if (user.nickname && user.nickname.trim()) {
    return user.nickname.trim().charAt(0).toUpperCase();
  }

  // 如果没有昵称，使用邮箱用户名首字母
  if (user.email && user.email.trim()) {
    const username = user.email.split('@')[0];
    if (username && username.trim()) {
      return username.trim().charAt(0).toUpperCase();
    }
  }

  // 默认兜底
  return 'U';
}

/**
 * 生成用户显示名称，按优先级顺序：
 * 1. 用户昵称
 * 2. 邮箱用户名
 * 3. 完整邮箱地址
 */
export function getDisplayName(user: { nickname?: string; email?: string } | null | undefined): string {
  if (!user) {
    return 'User';
  }

  // 优先使用昵称
  if (user.nickname && user.nickname.trim()) {
    return user.nickname.trim();
  }

  // 如果没有昵称，使用邮箱用户名
  if (user.email && user.email.trim()) {
    const username = user.email.split('@')[0];
    if (username && username.trim()) {
      return username.trim();
    }
    // 如果邮箱用户名为空，返回完整邮箱
    return user.email;
  }

  // 默认兜底
  return 'User';
} 