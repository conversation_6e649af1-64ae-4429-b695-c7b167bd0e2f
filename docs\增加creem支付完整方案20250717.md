# Creem支付集成完整方案

> **项目目标**: 在生产环境中成功接入creem支付，让用户能正常支付  
> **前提条件**: 必须先在测试环境下测试成功  
> **方案类型**: 完全隔离设计，与现有Stripe系统互不干扰  
> **创建日期**: 2025-01-17

## 🎯 项目现状理解

- ✅ 现有完整的Stripe支付模块（已闲置不用）
- ✅ Creem将成为主要且唯一的支付方式
- ✅ 需要完全隔离的Creem支付系统
- ✅ Stripe代码保留但不启用（备用）

## 📋 完整实施方案

### **Phase 1: 测试环境准备 (1-2天)**

#### 1.1 Creem测试环境设置
```bash
□ 登录creem仪表板，激活测试模式
□ 创建测试产品（映射现有定价方案）
□ 获取测试API密钥和Webhook密钥
□ 配置测试webhook端点到项目
```

#### 1.2 环境变量配置
```bash
# .env.development
# Stripe配置保留但不启用
STRIPE_PUBLIC_KEY=pk_test_...
STRIPE_PRIVATE_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
ENABLE_STRIPE_PAYMENT=false  # 关闭Stripe

# Creem作为主要支付方式
CREEM_ENVIRONMENT=test
CREEM_API_URL=https://test-api.creem.io
CREEM_TEST_API_KEY=your_test_api_key
CREEM_TEST_WEBHOOK_SECRET=your_test_webhook_secret
ENABLE_CREEM_PAYMENT=true    # 启用Creem
DEFAULT_PAYMENT_PROVIDER=creem  # 默认支付方式
```

### **Phase 2: Creem支付模块开发 (3-5天)**

#### 2.1 数据库修改
```sql
-- 修改支付提供商默认值为creem
-- 1. 添加支付提供商字段，直接默认为creem
ALTER TABLE orders ADD COLUMN payment_provider VARCHAR(50) DEFAULT 'creem';

-- 2. 添加creem专用字段
ALTER TABLE orders ADD COLUMN creem_session_id VARCHAR(255);
ALTER TABLE orders ADD COLUMN creem_customer_id VARCHAR(255);
ALTER TABLE orders ADD COLUMN creem_product_id VARCHAR(255);

-- 3. 创建索引提高查询性能
CREATE INDEX idx_orders_payment_provider ON orders(payment_provider);
CREATE INDEX idx_orders_creem_session_id ON orders(creem_session_id);
CREATE INDEX idx_orders_creem_customer_id ON orders(creem_customer_id);
CREATE INDEX idx_orders_creem_product_id ON orders(creem_product_id);
```

#### 2.2 文件结构（完全隔离设计）
```
新增Creem支付模块：
├── lib/creem/
│   ├── client.ts           # Creem SDK核心客户端
│   ├── config.ts          # 环境配置管理
│   ├── types.ts           # Creem类型定义
│   ├── utils.ts           # 工具函数
│   └── constants.ts       # 常量定义
├── services/creem-order.ts  # Creem订单业务逻辑
├── models/creem-order.ts    # Creem订单数据操作
├── app/api/creem/
│   ├── checkout/route.ts   # Creem结账API
│   ├── notify/route.ts     # Creem Webhook处理
│   └── customer-portal/route.ts # 客户门户
├── app/[locale]/payment/
    ├── success/[session_id]/page.tsx  # 支付成功页
    └── cancel/page.tsx                # 支付取消页
```

#### 2.3 核心实现重点

**A. 主要定价页面修改**
```typescript
// components/blocks/pricing/index.tsx
// 修改为使用Creem支付，移除Stripe调用
const handleCheckout = async (item: PricingItem) => {
  // 改为调用 /api/creem/checkout
  const response = await fetch("/api/creem/checkout", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(params),
  });
  // 重定向到Creem支付页面
};
```

**B. 替换现有支付API**
```typescript
// app/api/checkout/route.ts → 修改为使用Creem
// 或者创建新的 app/api/creem/checkout/route.ts
```

**C. 新的Webhook处理**
```typescript
// app/api/creem/notify/route.ts
// 完全替代 stripe-notify 的功能
```

### **Phase 3: 现有代码适配 (1-2天)**

#### 3.1 支付相关组件修改
```bash
□ 修改pricing组件调用Creem支付
□ 更新支付成功页面路由
□ 修改支付历史显示逻辑
□ 更新积分扣除检查逻辑
```

#### 3.2 服务层适配
```bash
□ 修改order.ts中的支付处理逻辑
□ 更新credit.ts中的积分逻辑
□ 适配用户服务中的支付相关功能
```

### **Phase 4: 测试环境完整验证 (2-3天)**

#### 4.1 功能测试
```bash
□ 使用测试卡：4242 4242 4242 4242
□ 完整支付流程：选择→支付→成功→积分到账
□ 测试所有定价方案（月付、年付、一次性）
□ 验证webhook事件处理
□ 测试支付失败和取消场景
```

#### 4.2 系统集成测试
```bash
□ 用户注册登录流程
□ 积分系统完整性验证
□ AI功能积分扣除测试
□ 支付历史记录验证
□ 多语言支持测试
```

#### 4.3 性能和稳定性测试
```bash
□ 支付流程响应时间测试
□ Webhook处理稳定性测试
□ 错误处理机制验证
□ 边界情况测试
```

### **Phase 5: 生产环境部署 (1-2天)**

#### 5.1 生产配置
```bash
# .env.production
CREEM_ENVIRONMENT=production
CREEM_API_URL=https://api.creem.io
CREEM_PROD_API_KEY=your_production_api_key
CREEM_PROD_WEBHOOK_SECRET=your_production_webhook_secret
ENABLE_CREEM_PAYMENT=true
ENABLE_STRIPE_PAYMENT=false  # 保持Stripe关闭
DEFAULT_PAYMENT_PROVIDER=creem
```

#### 5.2 部署策略
```bash
□ 数据库迁移执行
□ 生产环境webhook配置
□ SSL和安全配置验证
□ 监控和日志配置
□ 小规模真实交易测试
```

### **Phase 6: 生产验证和监控 (持续)**

#### 6.1 关键指标监控
```bash
□ 支付成功率监控 (目标: >95%)
□ Webhook处理成功率 (目标: >99%)
□ 支付流程响应时间 (目标: <3秒)
□ 用户支付体验反馈
□ 系统错误率和异常监控
```

#### 6.2 用户体验优化
```bash
□ 支付页面加载速度优化
□ 移动端支付体验优化
□ 支付错误提示优化
□ 多币种支持验证
```

## 🔄 关键要点总结

### 1. **支付方式策略**
- 🎯 **Creem为主要且唯一支付方式**
- 🔒 **Stripe代码保留但完全禁用**
- ⚡ **所有支付流程指向Creem**

### 2. **风险控制**
- ✅ **测试环境充分验证确保稳定性**
- ✅ **完善的错误处理和监控**
- ✅ **Stripe代码保留作为技术备用**
- ✅ **快速禁用机制（临时维护用）**

### 3. **验收标准**
- ✅ **Creem支付流程100%完整可用**
- ✅ **所有定价方案正常工作**
- ✅ **积分系统完美集成**
- ✅ **用户体验流畅**
- ✅ **系统稳定性达标**

### 4. **应急方案**
- 🚨 **如遇重大问题：临时禁用支付功能进行修复**
- 🔧 **必要时可快速重新启用Stripe（需要重新配置）**
- 📞 **Creem技术支持联系机制**

## 📋 项目优先级

1. **P0 (最高优先级)**: Creem支付核心功能实现
2. **P1 (高优先级)**: 测试环境完整验证  
3. **P2 (中优先级)**: 生产环境部署和监控
4. **P3 (低优先级)**: 用户体验优化和功能扩展

## 🔧 技术依赖

### NPM包依赖
```json
{
  "creem": "^0.3.36"  // Creem官方SDK
}
```

### 环境要求
- Node.js >= 18
- Next.js App Router
- Supabase数据库
- TypeScript支持

## 📚 参考资料

- [Creem官方文档](https://docs.creem.io)
- [Creem测试模式](https://docs.creem.io/test-mode)
- [Creem TypeScript SDK](https://docs.creem.io/sdk/typescript-sdk)
- [Creem NextJS模板](https://docs.creem.io/sdk/nextjs-template)

## ✅ 验收检查清单

### 测试环境验收
- [ ] 使用测试卡完成完整支付流程
- [ ] Webhook正确接收并处理支付事件
- [ ] 订单状态正确更新为"paid"
- [ ] 用户积分正确增加
- [ ] 与Stripe系统完全隔离运行
- [ ] 支付历史记录正确显示

### 生产环境验收
- [ ] 真实支付流程完全正常
- [ ] 支付成功率 > 95%
- [ ] Webhook处理成功率 > 99%
- [ ] 系统响应时间 < 3秒
- [ ] 用户反馈满意度良好
- [ ] 与现有系统稳定运行

---

**最后更新**: 2025-01-17  
**方案版本**: v1.0  
**负责人**: 开发团队
