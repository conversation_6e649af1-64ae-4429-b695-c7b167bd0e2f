'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import Link from 'next/link';

interface PaymentResult {
  success: boolean;
  message: string;
  orderId?: string;
  credits?: number;
}

export default function PaymentSuccessPage() {
  const searchParams = useSearchParams();
  const [result, setResult] = useState<PaymentResult | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const processPayment = async () => {
      try {
        // 获取 URL 参数
        const checkoutId = searchParams.get('checkout_id');
        const orderId = searchParams.get('order_id');
        const customerId = searchParams.get('customer_id');
        const productId = searchParams.get('product_id');
        const signature = searchParams.get('signature');

        console.log('Payment return parameters:', {
          checkoutId,
          orderId,
          customerId,
          productId,
          signature: signature?.substring(0, 20) + '...'
        });

        if (!checkoutId || !signature) {
          setResult({
            success: false,
            message: '支付参数不完整，请联系客服'
          });
          return;
        }

        // 调用后端 API 处理支付结果
        const response = await fetch('/api/creem/payment-return', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            checkout_id: checkoutId,
            order_id: orderId,
            customer_id: customerId,
            product_id: productId,
            signature: signature,
          }),
        });

        const data = await response.json();

        if (response.ok) {
          setResult({
            success: true,
            message: '支付成功！积分已添加到您的账户',
            orderId: data.orderId,
            credits: data.credits,
          });
        } else {
          setResult({
            success: false,
            message: data.error || '支付处理失败，请联系客服'
          });
        }

      } catch (error) {
        console.error('Payment processing error:', error);
        setResult({
          success: false,
          message: '处理支付结果时出错，请联系客服'
        });
      } finally {
        setLoading(false);
      }
    };

    processPayment();
  }, [searchParams]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-4" />
            <p className="text-gray-600">正在处理支付结果...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {result?.success ? (
              <CheckCircle className="h-16 w-16 text-green-600" />
            ) : (
              <AlertCircle className="h-16 w-16 text-red-600" />
            )}
          </div>
          <CardTitle className={result?.success ? 'text-green-600' : 'text-red-600'}>
            {result?.success ? '支付成功！' : '支付失败'}
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-gray-600">{result?.message}</p>
          
          {result?.success && result.credits && (
            <div className="bg-green-50 p-4 rounded-lg">
              <p className="text-green-800">
                已为您添加 <span className="font-bold">{result.credits}</span> 积分
              </p>
            </div>
          )}

          {result?.orderId && (
            <p className="text-sm text-gray-500">
              订单号: {result.orderId}
            </p>
          )}

          <div className="flex flex-col gap-2 pt-4">
            <Button asChild>
              <Link href="/dashboard">
                返回控制台
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/my-orders">
                查看订单
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
