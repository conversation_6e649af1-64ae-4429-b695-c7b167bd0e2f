"use client";

import { Check, Loader } from "lucide-react";
import { PricingItem, Pricing as PricingType } from "@/types/blocks/pricing";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useEffect, useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { useAppContext } from "@/contexts/app";
import { useSession } from "next-auth/react";

export default function Pricing({ pricing }: { pricing: PricingType }) {
  if (pricing.disabled) {
    return null;
  }

  const { user, setShowSignModal } = useAppContext();

  const [group, setGroup] = useState(pricing.groups?.[0]?.name);
  const [isLoading, setIsLoading] = useState(false);
  const [productId, setProductId] = useState<string | null>(null);

  const handleCheckout = async (item: PricingItem, cn_pay: boolean = false) => {
    try {
      if (!user) {
        setShowSignModal(true);
        return;
      }

      setIsLoading(true);
      setProductId(item.product_id);

      // 映射产品类型到Creem产品类型
      const getProductType = (interval: string) => {
        switch (interval) {
          case 'month':
            return 'MONTHLY';
          case 'year':
            return 'YEARLY';
          case 'one-time':
            return 'ONETIME';
          default:
            return 'ONETIME';
        }
      };

      const params = {
        productType: getProductType(item.interval),
        userEmail: user.email,
        userName: user.name,
      };

      const response = await fetch("/api/creem/checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
      });

      if (response.status === 401) {
        setIsLoading(false);
        setProductId(null);
        setShowSignModal(true);
        return;
      }

      const result = await response.json();
      
      if (!result.success) {
        toast.error(result.error || "创建支付会话失败");
        return;
      }

      // 重定向到Creem支付页面
      if (result.data.checkout_url) {
        window.location.href = result.data.checkout_url;
      } else {
        toast.error("获取支付链接失败");
      }
    } catch (e) {
      console.error("Creem checkout failed: ", e);
      toast.error("支付创建失败");
    } finally {
      setIsLoading(false);
      setProductId(null);
    }
  };

  useEffect(() => {
    if (pricing.items) {
      setGroup(pricing.items[0].group);
      setProductId(pricing.items[0].product_id);
      setIsLoading(false);
    }
  }, [pricing.items]);

  // 分离订阅计划和积分包
  const subscriptionPlans = pricing.items?.filter((item) => 
    item.interval === "month" || item.interval === "year"
  ) || [];
  
  const creditPacks = pricing.items?.filter((item) => 
    item.interval === "one-time"
  ) || [];

  const PricingCard = ({ item, index }: { item: PricingItem; index: number }) => (
    <div
      key={index}
      className={`rounded-lg p-6 ${
        item.is_featured
          ? "border-primary border-2 bg-card text-card-foreground"
          : "border-muted border"
      }`}
    >
      <div className="flex h-full flex-col justify-between gap-5">
        <div>
          <div className="flex items-center gap-2 mb-4">
            {item.title && (
              <h3 className="text-xl font-semibold">
                {item.title}
              </h3>
            )}
            <div className="flex-1"></div>
            {item.label && (
              <Badge
                variant="outline"
                className="border-primary bg-primary px-1.5 text-primary-foreground"
              >
                {item.label}
              </Badge>
            )}
          </div>
          <div className="flex items-end gap-2 mb-4">
            {item.original_price && (
              <span className="text-xl text-muted-foreground font-semibold line-through">
                {item.original_price}
              </span>
            )}
            {item.price && (
              <span className="text-5xl font-semibold">
                {item.price}
              </span>
            )}
            {item.unit && (
              <span className="block font-semibold">
                {item.unit}
              </span>
            )}
          </div>
          {item.description && (
            <p className="text-muted-foreground">
              {item.description}
            </p>
          )}
          {item.features_title && (
            <p className="mb-3 mt-6 font-semibold">
              {item.features_title}
            </p>
          )}
          {item.features && (
            <ul className="flex flex-col gap-3">
              {item.features.map((feature, fi) => {
                return (
                  <li className="flex gap-2" key={`feature-${fi}`}>
                    <Check className="mt-1 size-4 shrink-0" />
                    {feature}
                  </li>
                );
              })}
            </ul>
          )}
        </div>
        <div className="flex flex-col gap-2">
          {item.button && (
            <Button
              className="w-full flex items-center justify-center gap-2 font-semibold"
              disabled={isLoading}
              onClick={() => {
                if (isLoading) {
                  return;
                }
                handleCheckout(item);
              }}
            >
              {(!isLoading ||
                (isLoading && productId !== item.product_id)) && (
                <p>{item.button.title}</p>
              )}

              {isLoading && productId === item.product_id && (
                <p>{item.button.title}</p>
              )}
              {isLoading && productId === item.product_id && (
                <Loader className="mr-2 h-4 w-4 animate-spin" />
              )}
              {item.button.icon && (
                <Icon name={item.button.icon} className="size-4" />
              )}
            </Button>
          )}
          {item.tip && (
            <p className="text-muted-foreground text-sm mt-2">
              {item.tip}
            </p>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <section id={pricing.name} className="py-16">
      <div className="container">
        <div className="mx-auto mb-12 text-center">
          <h2 className="mb-4 text-4xl font-semibold lg:text-5xl">
            {pricing.title}
          </h2>
          <p className="text-muted-foreground lg:text-lg">
            {pricing.description}
          </p>
        </div>

        {/* 订阅计划部分 */}
        <div className="w-full flex flex-col items-center gap-8">
          <div className="w-full text-center">
            <h3 className="text-2xl font-semibold mb-2">Subscription Plans</h3>
            <p className="text-muted-foreground">Choose a plan that fits your usage</p>
          </div>
          
          <div className="w-full mt-0 grid gap-6 grid-cols-1 md:grid-cols-3 lg:grid-cols-3">
            {subscriptionPlans.map((item, index) => (
              <PricingCard key={item.product_id} item={item} index={index} />
            ))}
          </div>

          {/* 分隔线 */}
          {creditPacks.length > 0 && (
            <>
              <div className="w-full border-t border-muted my-8"></div>
              
              {/* 积分包部分 */}
              <div className="w-full text-center">
                <h3 className="text-2xl font-semibold mb-2">Flexible Credit Packs</h3>
                <p className="text-muted-foreground">Pay as you go, perfect for occasional usage</p>
              </div>
              
              <div className="flex justify-center">
                <div className="w-full max-w-sm">
                  {creditPacks.map((item, index) => (
                    <PricingCard key={item.product_id} item={item} index={index} />
                  ))}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </section>
  );
}
