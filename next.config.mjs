import bundleAnalyzer from "@next/bundle-analyzer";
import createNextIntlPlugin from "next-intl/plugin";
import mdx from "@next/mdx";

const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === "true",
});

const withNextIntl = createNextIntlPlugin();

const withMDX = mdx({
  options: {
    remarkPlugins: [],
    rehypePlugins: [],
  },
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  reactStrictMode: false,
  pageExtensions: ["ts", "tsx", "js", "jsx", "md", "mdx"],
  
  // DeerFlow: ESLint configuration
  eslint: {
    ignoreDuringBuilds: true,
  },
  
  // DeerFlow: Turbopack configuration for development mode (experimental)
  experimental: {
    mdxRs: true,
  },

  // DeerFlow: Webpack configuration for production mode
  webpack: (config) => {
    // Add raw-loader for .md files
    config.module.rules.push({
      test: /\.md$/,
      use: "raw-loader",
    });
    return config;
  },

  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "*",
      },
    ],
  },
  
  // DeerFlow: Rewrites to reduce 404 noise (robots.txt is handled by public/robots.txt)
  async rewrites() {
    return [
      // Handle favicon requests to reduce 404 noise
      {
        source: '/favicon.ico',
        destination: '/favicon.ico',
      },
    ];
  },
  
  async redirects() {
    return [];
  },
};

// Configuration is already merged with experimental flags

export default withBundleAnalyzer(withNextIntl(withMDX(nextConfig)));
