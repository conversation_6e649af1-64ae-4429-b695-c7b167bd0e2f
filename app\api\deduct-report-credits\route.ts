import { respErr, respData } from "@/lib/resp";
import { decreaseCredits, CreditsTransType } from "@/services/credit";
import { getUserUuid } from "@/services/user";

export async function POST(req: Request) {
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    const { credits } = await req.json();
    if (!credits || credits <= 0) {
      return respErr("invalid credits amount");
    }

    // 扣除积分
    await decreaseCredits({
      user_uuid,
      trans_type: CreditsTransType.ReportGeneration, // 使用报告生成类型
      credits: credits,
    });

    return respData({ message: "积分扣除成功" });
  } catch (error) {
    console.error("积分扣除失败:", error);
    return respErr("积分扣除失败");
  }
} 