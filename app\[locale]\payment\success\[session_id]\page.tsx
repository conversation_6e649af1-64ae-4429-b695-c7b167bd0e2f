// app/[locale]/payment/success/[session_id]/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CheckCircle, Loader2, AlertCircle } from 'lucide-react';

interface PaymentSuccessPageProps {
  params: {
    session_id: string;
    locale: string;
  };
}

interface PaymentSession {
  id: string;
  status: string;
  amount: number;
  currency: string;
  product_id: string;
  customer_email: string;
}

export default function PaymentSuccessPage({ params }: PaymentSuccessPageProps) {
  const { session_id } = params;
  const { data: session } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [paymentSession, setPaymentSession] = useState<PaymentSession | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const verifyPayment = async () => {
      try {
        const response = await fetch(`/api/creem/checkout?session_id=${session_id}`);
        const data = await response.json();
        
        if (response.ok && data.success) {
          setPaymentSession(data.data);
        } else {
          setError('支付验证失败');
        }
      } catch (error) {
        setError('支付验证出错');
      } finally {
        setLoading(false);
      }
    };

    if (session_id) {
      verifyPayment();
    }
  }, [session_id]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">正在验证支付状态...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              支付验证失败
            </CardTitle>
            <CardDescription>
              {error}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => router.push('/pricing')}
              className="w-full"
            >
              返回定价页面
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getProductName = (productId: string) => {
    const productMap = {
      'monthly_credits': '月度积分包',
      'yearly_credits': '年度积分包',
      'onetime_credits': '一次性积分包',
    };
    return productMap[productId as keyof typeof productMap] || productId;
  };

  const getCreditsAmount = (productId: string) => {
    const creditsMap = {
      'monthly_credits': 1000,
      'yearly_credits': 12000,
      'onetime_credits': 100,
    };
    return creditsMap[productId as keyof typeof creditsMap] || 0;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <CheckCircle className="h-16 w-16 text-green-500" />
          </div>
          <CardTitle className="text-2xl text-green-600">
            支付成功！
          </CardTitle>
          <CardDescription>
            您的支付已成功处理，积分即将到账
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {paymentSession && (
            <>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">产品</span>
                  <span className="font-medium">
                    {getProductName(paymentSession.product_id)}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">积分数量</span>
                  <Badge variant="secondary">
                    {getCreditsAmount(paymentSession.product_id).toLocaleString()} 积分
                  </Badge>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">支付金额</span>
                  <span className="font-medium">
                    {paymentSession.currency} {paymentSession.amount}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">支付状态</span>
                  <Badge variant="default" className="bg-green-500">
                    已完成
                  </Badge>
                </div>
              </div>
              
              <Separator />
              
              <div className="text-center text-sm text-muted-foreground">
                <p>积分将在几分钟内自动添加到您的账户</p>
                <p className="mt-1">如有问题请联系客服</p>
              </div>
            </>
          )}
          
          <div className="flex flex-col gap-3">
            <Button 
              onClick={() => router.push('/console')}
              className="w-full"
            >
              前往控制台
            </Button>
            
            <Button 
              variant="outline"
              onClick={() => router.push('/pricing')}
              className="w-full"
            >
              继续购买
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 