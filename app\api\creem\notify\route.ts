// app/api/creem/notify/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCreemClient } from '@/lib/creem/client';
import { getCreemOrderBySessionId, updateCreemOrderStatus } from '@/models/creem-order';
import { getUserCredits } from '@/services/credit';
import { CREEM_PRICING } from '@/lib/creem/config';
import type { CreemWebhookEvent } from '@/lib/creem/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('creem-signature') || '';

    // 验证Webhook签名
    const creemClient = getCreemClient();
    const isValid = creemClient.verifyWebhookSignature(body, signature);
    
    if (!isValid) {
      console.error('Invalid webhook signature');
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 401 }
      );
    }

    const event: CreemWebhookEvent = JSON.parse(body);
    
    console.log('Received Creem webhook event:', event.type, event.id);

    // 处理不同类型的webhook事件
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(event);
        break;
      
      case 'payment.succeeded':
        await handlePaymentSucceeded(event);
        break;
      
      case 'checkout.session.cancelled':
        await handleCheckoutCancelled(event);
        break;
      
      case 'payment.failed':
        await handlePaymentFailed(event);
        break;
      
      default:
        console.log('Unhandled webhook event type:', event.type);
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Webhook processing error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

async function handleCheckoutCompleted(event: CreemWebhookEvent) {
  try {
    const session = event.data.object;
    
    // 根据session ID查找订单
    const order = await getCreemOrderBySessionId(session.id);
    if (!order) {
      console.error('Order not found for session:', session.id);
      return;
    }

    // 更新订单状态为已支付
    await updateCreemOrderStatus(order.id, 'paid');
    
         // 获取产品信息并给用户添加积分
     const productType = getProductTypeFromId(order.product_id);
     if (productType) {
       const pricing = CREEM_PRICING[productType];
       // TODO: 实现积分添加逻辑
       // await addUserCredits(order.user_uuid, pricing.credits);
       
       console.log(`Need to add ${pricing.credits} credits to user ${order.user_uuid}`);
     }

    console.log('Checkout completed successfully for order:', order.id);
  } catch (error) {
    console.error('Error handling checkout completed:', error);
  }
}

async function handlePaymentSucceeded(event: CreemWebhookEvent) {
  try {
    const session = event.data.object;
    
    // 根据session ID查找订单
    const order = await getCreemOrderBySessionId(session.id);
    if (!order) {
      console.error('Order not found for session:', session.id);
      return;
    }

    // 更新订单状态为已支付
    await updateCreemOrderStatus(order.id, 'paid');
    
    console.log('Payment succeeded for order:', order.id);
  } catch (error) {
    console.error('Error handling payment succeeded:', error);
  }
}

async function handleCheckoutCancelled(event: CreemWebhookEvent) {
  try {
    const session = event.data.object;
    
    // 根据session ID查找订单
    const order = await getCreemOrderBySessionId(session.id);
    if (!order) {
      console.error('Order not found for session:', session.id);
      return;
    }

    // 更新订单状态为已取消
    await updateCreemOrderStatus(order.id, 'cancelled');
    
    console.log('Checkout cancelled for order:', order.id);
  } catch (error) {
    console.error('Error handling checkout cancelled:', error);
  }
}

async function handlePaymentFailed(event: CreemWebhookEvent) {
  try {
    const session = event.data.object;
    
    // 根据session ID查找订单
    const order = await getCreemOrderBySessionId(session.id);
    if (!order) {
      console.error('Order not found for session:', session.id);
      return;
    }

    // 更新订单状态为失败
    await updateCreemOrderStatus(order.id, 'failed');
    
    console.log('Payment failed for order:', order.id);
  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

function getProductTypeFromId(productId: string): keyof typeof CREEM_PRICING | null {
  const productMap = {
    'monthly_credits': 'MONTHLY' as const,
    'yearly_credits': 'YEARLY' as const,
    'onetime_credits': 'ONETIME' as const,
  };
  
  return productMap[productId as keyof typeof productMap] || null;
}

// 支持GET方法用于验证webhook端点
export async function GET(request: NextRequest) {
  return NextResponse.json({ 
    message: 'Creem webhook endpoint is active',
    timestamp: new Date().toISOString()
  });
}