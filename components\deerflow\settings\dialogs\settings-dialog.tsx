// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { Settings } from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useIsMobile } from "@/hooks/use-mobile";

import { Tooltip } from "@/components/deerflow/deer-flow/tooltip";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { useReplay } from "@/lib/deerflow/core/replay";
import {
  type SettingsState,
  changeSettings,
  saveSettings,
  useSettingsStore,
} from "@/lib/deerflow/core/store/settings-store";
import { cn } from "@/lib/utils";

import { SETTINGS_TABS } from "../tabs";

export function SettingsDialog() {
  const { isReplay } = useReplay();
  const isMobile = useIsMobile();
  const [activeTabId, setActiveTabId] = useState(SETTINGS_TABS[0]!.id);
  const [open, setOpen] = useState(false);
  const [settings, setSettings] = useState(useSettingsStore.getState());
  const [changes, setChanges] = useState<Partial<SettingsState>>({});

  const handleTabChange = useCallback(
    (newChanges: Partial<SettingsState>) => {
      setTimeout(() => {
        if (open) {
          setChanges((prev) => ({
            ...prev,
            ...newChanges,
          }));
        }
      }, 0);
    },
    [open],
  );

  const handleSave = useCallback(() => {
    if (Object.keys(changes).length > 0) {
      const newSettings: SettingsState = {
        ...settings,
        ...changes,
      };
      setSettings(newSettings);
      setChanges({});
      changeSettings(newSettings);
      saveSettings();
    }
    setOpen(false);
  }, [settings, changes]);

  const handleOpen = useCallback(() => {
    setSettings(useSettingsStore.getState());
  }, []);

  const handleClose = useCallback(() => {
    setChanges({});
  }, []);

  useEffect(() => {
    if (open) {
      handleOpen();
    } else {
      handleClose();
    }
  }, [open, handleOpen, handleClose]);

  const mergedSettings = useMemo<SettingsState>(() => {
    return {
      ...settings,
      ...changes,
    };
  }, [settings, changes]);

  if (isReplay) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <Tooltip title="Settings">
        <DialogTrigger asChild>
          <Button variant="ghost" size="icon">
            <Settings />
          </Button>
        </DialogTrigger>
      </Tooltip>
      <DialogContent className={cn(
        "max-h-[90vh] overflow-auto",
        // 桌面端保持原有宽�?        !isMobile && "sm:max-w-[850px]",
        // 移动端使用全屏宽度，留小边距
        isMobile && "w-[95vw] max-w-[95vw] h-[90vh]"
      )}>
        <DialogHeader>
          <DialogTitle>DeerFlow Settings</DialogTitle>
          <DialogDescription>
            Manage your DeerFlow settings here.
          </DialogDescription>
        </DialogHeader>
        <Tabs value={activeTabId}>
          <div className={cn(
            "w-full overflow-auto border-y",
            // 桌面端使用水平布局
            !isMobile && "flex h-120",
            // 移动端使用垂直布局
            isMobile && "flex flex-col h-[60vh]"
          )}>
            <ul className={cn(
              "flex shrink-0 p-1",
              // 桌面端：垂直标签�?              !isMobile && "w-50 border-r",
              // 移动端：水平标签�?              isMobile && "w-full border-b overflow-x-auto"
            )}>
              <div className={cn(
                "size-full",
                // 移动端：水平排列标签
                isMobile && "flex gap-1 min-w-max"
              )}>
                {SETTINGS_TABS.map((tab) => (
                  <li
                    key={tab.id}
                    className={cn(
                      "hover:accent-foreground hover:bg-accent flex cursor-pointer items-center gap-1.5 rounded px-2",
                      // 桌面端样�?                      !isMobile && "mb-1 h-8 w-full",
                      // 移动端样式：更紧�?                      isMobile && "h-10 whitespace-nowrap flex-shrink-0",
                      activeTabId === tab.id &&
                        "!bg-primary !text-primary-foreground",
                    )}
                    onClick={() => setActiveTabId(tab.id)}
                  >
                    <tab.icon size={16} />
                    <span className={cn(
                      // 移动端隐藏文字，只显示图�?                      isMobile && "hidden sm:inline"
                    )}>{tab.label}</span>
                    {tab.badge && (
                      <Badge
                        variant="outline"
                        className={cn(
                          "border-muted-foreground text-muted-foreground px-1 py-0 text-xs",
                          !isMobile && "ml-auto",
                          isMobile && "hidden sm:inline-flex",
                          activeTabId === tab.id &&
                            "border-primary-foreground text-primary-foreground",
                        )}
                      >
                        {tab.badge}
                      </Badge>
                    )}
                  </li>
                ))}
              </div>
            </ul>
            <div className="min-w-0 flex-grow">
              <div
                id="settings-content-scrollable"
                className={cn(
                  "size-full overflow-auto",
                  // 桌面端保持原有内边距
                  !isMobile && "p-4",
                  // 移动端减少内边距
                  isMobile && "p-2"
                )}
              >
                {SETTINGS_TABS.map((tab) => (
                  <TabsContent key={tab.id} value={tab.id}>
                    <tab.component
                      settings={mergedSettings}
                      onChange={handleTabChange}
                    />
                  </TabsContent>
                ))}
              </div>
            </div>
          </div>
        </Tabs>
        <DialogFooter className={cn(
          "pt-6 border-t border-border/50",
          // 移动端：全宽按钮，垂直排列，增加间距
          isMobile && "flex-col-reverse space-x-0 space-y-3 space-y-reverse gap-0 px-1",
          // 桌面端保持原有间距
          !isMobile && "gap-3"
        )}>
          <Button 
            variant="outline" 
            onClick={() => setOpen(false)}
            className={cn(
              "transition-all duration-200 ease-in-out active:scale-[0.98]",
              // 移动端样式：更大更圆润
              isMobile && [
                "w-full h-12 rounded-xl text-base font-medium shadow-sm hover:shadow-md border-2",
                "bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700",
                "hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800",
                "dark:bg-gray-800/80 dark:border-gray-600 dark:text-gray-200",
                "dark:hover:bg-gray-700 dark:hover:border-gray-500"
              ],
              // 桌面端保持原有样式
              !isMobile && "hover:shadow-sm"
            )}
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            onClick={handleSave}
            className={cn(
              "transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl active:scale-[0.98]",
              // 移动端样式：更大更圆润，美丽的渐变背景
              isMobile && [
                "w-full h-12 rounded-xl text-base font-semibold text-white",
                "bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700",
                "hover:from-blue-600 hover:via-blue-700 hover:to-blue-800",
                "dark:from-blue-600 dark:via-blue-700 dark:to-blue-800",
                "dark:hover:from-blue-700 dark:hover:via-blue-800 dark:hover:to-blue-900",
                "shadow-blue-500/25 hover:shadow-blue-600/30"
              ],
              // 桌面端样式
              !isMobile && [
                "w-24 bg-gradient-to-r from-blue-600 to-blue-700", 
                "hover:from-blue-700 hover:to-blue-800",
                "dark:from-blue-600 dark:to-blue-700",
                "dark:hover:from-blue-700 dark:hover:to-blue-800"
              ]
            )}
          >
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
