import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

/**
 * Send email verification email
 * @param to Recipient email
 * @param verifyUrl Verification link
 */
export async function sendVerificationEmail(to: string, verifyUrl: string) {
  const subject = 'Email Verification - DeerFlow';
  const html = `
    <div>
      <p>Hello,</p>
      <p>Please click the link below to complete your email verification:</p>
      <a href="${verifyUrl}">${verifyUrl}</a>
      <p>If this was not you, please ignore this email.</p>
    </div>
  `;
  try {
    const data = await resend.emails.send({
      from: '<EMAIL>', // 已验证域名邮箱
      to,
      subject,
      html,
    });
    return data;
  } catch (error) {
    console.error('Failed to send verification email:', error);
    throw error;
  }
} 