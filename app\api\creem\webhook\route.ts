// app/api/creem/webhook/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCreemConfig } from '@/lib/creem/config';
import { updateCreemOrder } from '@/models/creem-order';
import { addUserCredits } from '@/models/user-credits';

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('creem-signature');
    
    console.log('Creem webhook received:', {
      hasSignature: !!signature,
      bodyLength: body.length,
      signature: signature?.substring(0, 20) + '...'
    });

    // 验证 webhook 签名
    const config = getCreemConfig();
    if (!signature) {
      console.error('Missing Creem signature');
      return NextResponse.json({ error: 'Missing signature' }, { status: 400 });
    }

    // TODO: 实现签名验证逻辑
    // 目前先跳过签名验证，在测试环境中

    const event = JSON.parse(body);
    console.log('Creem webhook event:', event);

    // 处理不同类型的事件
    switch (event.type) {
      case 'checkout.completed':
        await handleCheckoutCompleted(event.data);
        break;
      case 'checkout.failed':
        await handleCheckoutFailed(event.data);
        break;
      default:
        console.log('Unhandled webhook event type:', event.type);
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Creem webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

async function handleCheckoutCompleted(checkoutData: any) {
  try {
    console.log('Processing checkout completed:', checkoutData);

    const sessionId = checkoutData.id;
    
    // 根据 session_id 查找订单
    // TODO: 实现根据 creem_session_id 查找订单的功能
    
    // 更新订单状态
    // await updateCreemOrder(orderId, {
    //   status: 'paid',
    //   paid_at: new Date().toISOString(),
    // });

    // 给用户添加积分
    // await addUserCredits(userUuid, credits);

    console.log('Checkout completed processing finished');

  } catch (error) {
    console.error('Error processing checkout completed:', error);
    throw error;
  }
}

async function handleCheckoutFailed(checkoutData: any) {
  try {
    console.log('Processing checkout failed:', checkoutData);

    const sessionId = checkoutData.id;
    
    // 更新订单状态为失败
    // TODO: 实现订单失败处理

    console.log('Checkout failed processing finished');

  } catch (error) {
    console.error('Error processing checkout failed:', error);
    throw error;
  }
}
