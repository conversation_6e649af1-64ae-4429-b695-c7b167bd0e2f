import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServiceClient } from '@/models/db';
import bcrypt from 'bcryptjs';
import { sendVerificationEmail } from '@/lib/email';
import { nanoid } from 'nanoid';

const supabase = getSupabaseServiceClient();

export async function POST(req: NextRequest) {
  const { email, password } = await req.json();
  if (!email || !password) {
    return NextResponse.json({ error: 'Email and password are required' }, { status: 400 });
  }

  // 检查邮箱是否已注册
  const { data: existUser, error: existError } = await supabase
    .from('users')
    .select('id')
    .eq('email', email)
    .maybeSingle();
  if (existUser) {
    return NextResponse.json({ error: 'This email is already registered' }, { status: 409 });
  }

  // 密码加密
  const password_hash = await bcrypt.hash(password, 10);

  // 生成邮箱验证token
  const token = Math.random().toString(36).slice(2) + Date.now();
  const verifyUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/auth/verify-email?email=${encodeURIComponent(email)}&token=${token}`;

  // 生成uuid
  const uuid = nanoid();

  // 写入用户表
  const { data, error } = await supabase.from('users').insert([
    {
      uuid,
      email,
      password_hash,
      email_verified: false,
      created_at: new Date().toISOString(),
      verification_token: token,
    },
  ]);
  if (error) {
    console.error('Registration failed:', error);
    return NextResponse.json({ error: 'Registration failed', detail: error.message }, { status: 500 });
  }

  // 发送验证邮件
  try {
    await sendVerificationEmail(email, verifyUrl);
  } catch (e) {
    return NextResponse.json({ error: 'Failed to send verification email' }, { status: 500 });
  }

  return NextResponse.json({ 
    message: 'Registration successful. Please check your email to verify your account.',
    success: true 
  });
} 