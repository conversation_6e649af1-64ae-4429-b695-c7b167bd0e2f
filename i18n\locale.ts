import { Pathnames } from "next-intl/routing";

// 当前只启用英语，未来可以轻松添加其他语言
export const locales = ["en"];
// export const locales = ["en", "zh"]; // 取消注释即可启用中文

export const localeNames: any = {
  en: "English",
  // zh: "中文", // 保留配置，未来启用时取消注释
};

export const defaultLocale = "en";

export const localePrefix = "as-needed";

export const localeDetection =
  process.env.NEXT_PUBLIC_LOCALE_DETECTION === "true";

export const pathnames = {
  en: {
    "privacy-policy": "/privacy-policy",
    "terms-of-service": "/terms-of-service",
  },
  // 保留中文路径配置，未来启用时取消注释
  // zh: {
  //   "privacy-policy": "/隐私政策",
  //   "terms-of-service": "/服务条款",
  // },
} satisfies Pathnames<typeof locales>;
