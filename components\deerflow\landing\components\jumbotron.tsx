// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

// import { GithubFilled } from "@ant-design/icons";
import { ChevronRight } from "lucide-react";
import Link from "next/link";

// import { AuroraText } from "@/components/magicui/aurora-text";
import { FlickeringGrid } from "@/components/deerflow/magicui/flickering-grid";
import { Button } from "@/components/ui/button";
import { env } from "@/lib/env";

export function Jumbotron() {
  return (
    <section className="flex h-[95vh] w-full flex-col items-center justify-center pb-15">
      <FlickeringGrid
        id="deer-hero-bg"
        className={`absolute inset-0 z-0 [mask-image:radial-gradient(800px_circle_at_center,white,transparent)]`}
        squareSize={4}
        gridGap={4}
        color="#60A5FA"
        maxOpacity={0.133}
        flickerChance={0.1}
      />
      <FlickeringGrid
        id="deer-hero"
        className="absolute inset-0 z-0 translate-y-[2vh] mask-[url(/images/deer-hero.svg)] mask-size-[100vw] mask-center mask-no-repeat md:mask-size-[72vh]"
        squareSize={3}
        gridGap={6}
        color="#60A5FA"
        maxOpacity={0.64}
        flickerChance={0.12}
      />
      <div className="relative z-10 flex flex-col items-center justify-center gap-12">
        <h1 className="text-center text-5xl font-bold md:text-7xl">
          <span
            style={{
              background: "linear-gradient(90deg, #ff6b6b, #4ecdc4, #7b68ee)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              color: "transparent",
              textShadow: "0 2px 10px rgba(255, 255, 255, 0.15)",
              fontWeight: 800,
              fontSize: "1.1em"
            }}
          >
            Deep Research
          </span>
        </h1>
        <p className="max-w-4xl p-2 text-center text-sm opacity-85 md:text-2xl">
          DeerFlow: Your Deep Research companion. Powered by search engines and AI
          to deliver instant insights and comprehensive analysis.
        </p>
        <div className="flex gap-6">
          <Button className="flex text-lg w-36 md:w-42" size="lg" asChild>
            <Link
              target={
                env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY ? "_blank" : undefined
              }
              href={
                env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY
                  ? "https://github.com/bytedance/deer-flow"
                  : "/chat"
              }
            >
              Get Started <ChevronRight />
            </Link>
          </Button>
          {/* Learn More button removed */}
        </div>
      </div>
      {/* Bottom explanation removed */}
    </section>
  );
}

