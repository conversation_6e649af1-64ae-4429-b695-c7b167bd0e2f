import "@/app/globals.css";

import { getMessages, getTranslations } from "next-intl/server";
import { locales } from "@/i18n/locale";

import { AppContextProvider } from "@/contexts/app";
import { Inter as FontSans } from "next/font/google";
import { Metadata } from "next";
import { NextAuthSessionProvider } from "@/auth/session";
import { NextIntlClientProvider } from "next-intl";
import { ThemeProvider } from "@/providers/theme";
import { cn } from "@/lib/utils";
import Script from "next/script";

import { ClarityAnalyticsScript } from "@/components/analytics/ClarityAnalyticsScript";
import GoogleAnalytics from "@/components/analytics/google-analytics";
import Plausible from "@/components/analytics/plausible";
import { Toaster } from "@/components/deerflow/deer-flow/toaster";
import SignModal from "@/components/sign/modal";

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
});

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations();

  return {
    title: {
      template: `%s | DeerFlow`,
      default: "DeerFlow - Free Deep Research Tool",
    },
    description: "Free Deep Research Assistant for comprehensive knowledge exploration. Advanced AI platform integrating powerful language models with specialized research tools.",
    keywords: "AI research, deep research, knowledge exploration, research assistant, AI tools",
    authors: [{ name: "DeerFlow Team" }],
    creator: "DeerFlow",
    publisher: "DeerFlow",
    metadataBase: new URL(process.env.NEXT_PUBLIC_WEB_URL || "https://deerflow.net"),
    openGraph: {
      type: "website",
      locale: "en_US",
      url: process.env.NEXT_PUBLIC_WEB_URL || "https://deerflow.net",
      siteName: "DeerFlow",
      title: "DeerFlow - Free Deep Research Tool",
      description: "Free Deep Research Assistant for comprehensive knowledge exploration. Advanced AI platform integrating powerful language models with specialized research tools.",
    },
    twitter: {
      card: "summary_large_image",
      title: "DeerFlow - Free Deep Research Tool",
      description: "Free Deep Research Assistant for comprehensive knowledge exploration.",
      creator: "@deerflow",
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}>) {
  const { locale } = await params;
  const messages = await getMessages();
  const webUrl = process.env.NEXT_PUBLIC_WEB_URL || "";
  const googleAdsenseCode = process.env.NEXT_PUBLIC_GOOGLE_ADCODE || "";

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        {googleAdsenseCode && (
          <meta name="google-adsense-account" content={googleAdsenseCode} />
        )}

        <link rel="icon" href="/favicon.ico" />

        {locales && webUrl &&
          locales.map((loc) => (
            <link
              key={loc}
              rel="alternate"
              hrefLang={loc}
              href={`${webUrl}${loc === "en" ? "" : `/${loc}`}/`}
            />
          ))}
        {webUrl && <link rel="alternate" hrefLang="x-default" href={webUrl} />}

        {/* Define isSpace function globally to fix markdown-it issues with Next.js + Turbopack */}
        <Script id="markdown-it-fix" strategy="beforeInteractive">
          {`
            if (typeof window !== 'undefined' && typeof window.isSpace === 'undefined') {
              window.isSpace = function(code) {
                return code === 0x20 || code === 0x09 || code === 0x0A || code === 0x0B || code === 0x0C || code === 0x0D;
              };
            }
          `}
        </Script>
        
        {/* Analytics Scripts */}
        <GoogleAnalytics />
        <Plausible />
      </head>
      <body
        className={cn(
          "min-h-screen bg-background font-sans antialiased overflow-x-hidden",
          fontSans.variable
        )}
      >
        <NextIntlClientProvider messages={messages}>
          <NextAuthSessionProvider>
            <AppContextProvider>
              <ThemeProvider attribute="class" disableTransitionOnChange>
                {children}
                {/* Sign In Modal */}
                <SignModal />
              </ThemeProvider>
            </AppContextProvider>
          </NextAuthSessionProvider>
        </NextIntlClientProvider>
        
        {/* Microsoft Clarity Analytics */}
        <ClarityAnalyticsScript />
        
        {/* Toast Notifications */}
        <Toaster />
      </body>
    </html>
  );
}
