// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import Link from "next/link";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";

export function Logo() {
  const isMobile = useIsMobile();
  return (
    <Link
      className={cn(
        "opacity-70 transition-opacity duration-300 hover:opacity-100",
        // 桌面端保持原有样式
        !isMobile && "text-base",
        // 移动端使用更小的字体
        isMobile && "text-sm"
      )}
      href="/"
    >
      🦌 DeerFlow
    </Link>
  );
}

