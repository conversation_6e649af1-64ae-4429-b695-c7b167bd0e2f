// lib/creem/types.ts
// Creem类型定义

export interface CreemProduct {
  id: string;
  name: string;
  price: number;
  credits: number;
  currency: string;
  recurring?: boolean;
  interval?: 'month' | 'year';
}

export interface CreemCheckoutSession {
  id: string;
  url: string;
  customer_id?: string;
  status: 'pending' | 'completed' | 'cancelled';
  product_id: string;
  amount: number;
  currency: string;
  metadata?: Record<string, string>;
  created_at: string;
  expires_at: string;
}

export interface CreemCustomer {
  id: string;
  email: string;
  name?: string;
  created_at: string;
}

export interface CreemWebhookEvent {
  id: string;
  type: 'checkout.session.completed' | 'checkout.session.cancelled' | 'payment.succeeded' | 'payment.failed';
  data: {
    object: CreemCheckoutSession;
  };
  created_at: string;
}

export interface CreemPaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'succeeded' | 'failed' | 'cancelled';
  customer_id?: string;
  metadata?: Record<string, string>;
}

export interface CreemCreateCheckoutParams {
  product_id: string;
  customer_email: string;
  customer_name?: string;
  success_url: string;
  cancel_url: string;
  metadata?: Record<string, string>;
}

export interface CreemApiResponse<T> {
  data: T;
  error?: string;
  success: boolean;
}

export interface CreemApiError {
  message: string;
  code?: string;
  type?: string;
}