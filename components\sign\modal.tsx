"use client";

import * as React from "react";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  Drawer<PERSON>eader,
  Drawer<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { SiGithub, SiGmail, SiGoogle } from "react-icons/si";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { signIn } from "next-auth/react";
import { useAppContext } from "@/contexts/app";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { useTranslations } from "next-intl";
import { useState } from "react";

export default function SignModal() {
  const t = useTranslations();
  const { showSignModal, setShowSignModal } = useAppContext();

  const [open, setOpen] = React.useState(false);
  const isDesktop = useMediaQuery("(min-width: 768px)");

  if (isDesktop) {
    return (
      <Dialog open={showSignModal} onOpenChange={setShowSignModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{t("sign_modal.sign_in_title")}</DialogTitle>
            <DialogDescription>
              {t("sign_modal.sign_in_description")}
            </DialogDescription>
          </DialogHeader>
          <ProfileForm />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={showSignModal} onOpenChange={setShowSignModal}>
      <DrawerContent>
        <DrawerHeader className="text-left">
          <DrawerTitle>{t("sign_modal.sign_in_title")}</DrawerTitle>
          <DrawerDescription>
            {t("sign_modal.sign_in_description")}
          </DrawerDescription>
        </DrawerHeader>
        <ProfileForm className="px-4" />
        <DrawerFooter className="pt-4">
          <DrawerClose asChild>
            <Button variant="outline">{t("sign_modal.cancel_title")}</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}

function ProfileForm({ className }: React.ComponentProps<"form">) {
  const t = useTranslations();

  return (
    <div className={cn("grid items-start gap-4", className)}>
      {process.env.NEXT_PUBLIC_AUTH_EMAIL_ENABLED === "true" && (
        <EmailLoginFormModal />
      )}

      {((process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED === "true") || (process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED === "true")) && 
       (process.env.NEXT_PUBLIC_AUTH_EMAIL_ENABLED === "true") && (
        <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-gray-300">
          <span className="relative z-10 bg-white px-2 text-gray-500">
            {t("sign_modal.or")}
          </span>
        </div>
      )}

      {process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED === "true" && (
        <Button
          className="w-full flex items-center gap-2 bg-blue-600 text-white hover:bg-blue-700"
          onClick={() => {
            signIn("google");
          }}
        >
          <SiGoogle className="w-4 h-4 text-white" />
          {t("sign_modal.google_sign_in")}
        </Button>
      )}

      {process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED === "true" && (
        <Button
          variant="outline"
          className="w-full flex items-center gap-2 bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:text-gray-900 hover:border-gray-400"
          onClick={() => {
            signIn("github");
          }}
        >
          <SiGithub className="w-4 h-4 text-gray-900" />
          {t("sign_modal.github_sign_in")}
        </Button>
      )}
    </div>
  );
}

function EmailLoginFormModal() {
  const t = useTranslations();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [isSignUp, setIsSignUp] = useState(false);
  const { setShowSignModal } = useAppContext();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccess("");

    try {
      if (isSignUp) {
        // 注册流程
        const response = await fetch('/api/auth/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email, password }),
        });

        const data = await response.json();

        if (!response.ok) {
          setError(data.error || 'Registration failed');
        } else {
          setSuccess('Registration successful! Please check your email to complete verification, then return to sign in.');
          // 20秒后自动切换到登录模式
          setTimeout(() => {
            setIsSignUp(false);
            setSuccess("");
          }, 20000);
        }
      } else {
        // 登录流程
        const result = await signIn("email", {
          email,
          password,
          redirect: false,
        });

        if (result?.error) {
          setError(result.error === "CredentialsSignin" ? "Invalid email or password, or email not verified" : result.error);
        } else if (result?.ok) {
          // 登录成功，关闭弹窗并刷新页面
          setShowSignModal(false);
          window.location.reload();
        }
      }
    } catch (error) {
      setError(isSignUp ? "Registration failed, please try again" : "Login failed, please try again");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="grid gap-4">
        <div className="grid gap-2">
          <Label htmlFor="email">{t("sign_modal.email_title")}</Label>
          <Input
            id="email"
            type="email"
            placeholder={t("sign_modal.email_placeholder")}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
        </div>
        <div className="grid gap-2">
          <div className="flex items-center">
            <Label htmlFor="password">{t("sign_modal.password_title")}</Label>
            <a
              href="#"
              className="ml-auto text-sm underline-offset-4 hover:underline"
            >
              {t("sign_modal.forgot_password")}
            </a>
          </div>
          <Input
            id="password"
            type="password"
            placeholder={t("sign_modal.password_placeholder")}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
          />
        </div>
        {error && (
          <div className="text-sm text-red-500 text-center">{error}</div>
        )}
        {success && (
          <div className="text-sm text-green-500 text-center">{success}</div>
        )}
        <Button type="submit" variant="outline" className="w-full flex items-center gap-2 bg-white !text-gray-900 border-gray-300 hover:bg-gray-50 hover:!text-gray-900 hover:border-gray-400" disabled={isLoading}>
          <SiGmail className="w-4 h-4 text-gray-600" />
          {isLoading 
            ? t("sign_modal.continue") + "..." 
            : isSignUp 
              ? t("sign_modal.sign_up_title") 
              : t("sign_modal.email_sign_in")
          }
        </Button>
      </div>
      <div className="text-center text-sm mt-4">
        {isSignUp ? (
          <>
            Already have an account?{" "}
            <button
              type="button"
              onClick={() => {
                setIsSignUp(false);
                setError("");
                setSuccess("");
              }}
              className="underline underline-offset-4 text-blue-600 hover:text-blue-700"
            >
              Sign In
            </button>
          </>
        ) : (
          <>
            {t("sign_modal.no_account")}{" "}
            <button
              type="button"
              onClick={() => {
                setIsSignUp(true);
                setError("");
                setSuccess("");
              }}
              className="underline underline-offset-4 text-blue-600 hover:text-blue-700"
            >
              {t("sign_modal.sign_up_title")}
            </button>
          </>
        )}
      </div>
    </form>
  );
}
