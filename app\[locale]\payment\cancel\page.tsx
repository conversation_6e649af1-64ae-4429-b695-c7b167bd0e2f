// app/[locale]/payment/cancel/page.tsx
'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { XCircle } from 'lucide-react';

export default function PaymentCancelPage() {
  const router = useRouter();

  useEffect(() => {
    // 清除任何正在进行的支付状态
    // 这里可以添加清理逻辑
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <XCircle className="h-16 w-16 text-orange-500" />
          </div>
          <CardTitle className="text-2xl text-orange-600">
            支付已取消
          </CardTitle>
          <CardDescription>
            您的支付已被取消，没有扣除任何费用
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="text-center text-sm text-muted-foreground">
            <p>如果您遇到任何问题，请联系客服</p>
          </div>
          
          <div className="flex flex-col gap-3">
            <Button 
              onClick={() => router.push('/pricing')}
              className="w-full"
            >
              返回定价页面
            </Button>
            
            <Button 
              variant="outline"
              onClick={() => router.push('/console')}
              className="w-full"
            >
              前往控制台
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 