// app/api/creem/customer-portal/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { getCreemClient } from '@/lib/creem/client';

export async function POST(request: NextRequest) {
  try {
    // 验证用户登录状态
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '用户未登录' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { customer_id } = body;

    if (!customer_id) {
      return NextResponse.json(
        { error: '缺少客户ID' },
        { status: 400 }
      );
    }

    // 创建客户门户会话
    const creemClient = getCreemClient();
    const portalSession = await creemClient.createCustomerPortalSession(customer_id);

    return NextResponse.json({
      success: true,
      data: {
        portal_url: portalSession.url,
        session_id: portalSession.id,
      },
    });

  } catch (error) {
    console.error('Customer portal error:', error);
    return NextResponse.json(
      { error: '创建客户门户失败' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({ 
    message: 'Creem customer portal endpoint is active',
    timestamp: new Date().toISOString()
  });
}
