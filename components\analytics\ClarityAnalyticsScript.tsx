"use client";

import Clarity from "@microsoft/clarity";
import { useEffect } from "react";

// 直接硬编码Clarity项目ID
const clarityProjectId = "rmtgv2f25p";

export function ClarityAnalyticsScript() {
  useEffect(() => {
    // 确保只在客户端执行
    if (typeof window !== "undefined") {
      // 初始化Clarity
      Clarity.init(clarityProjectId);
    }

    // 清理函数
    return () => {
      // Clarity没有提供显式的清理方法
    };
  }, []);

  // 这个组件不渲染任何内容
  return null;
}

export function useClarityAnalytics() {
  const trackEvent = (eventName: string) => {
    if (typeof window === "undefined") {
      return;
    }

    // 使用Clarity的事件API
    Clarity.event(eventName);
  };

  const setTag = (key: string, value: string | string[]) => {
    if (typeof window === "undefined") {
      return;
    }

    // 使用Clarity的标签API
    Clarity.setTag(key, value);
  };

  const identify = (
    customId: string,
    customSessionId?: string,
    customPageId?: string,
    friendlyName?: string
  ) => {
    if (typeof window === "undefined") {
      return;
    }

    // 使用Clarity的识别API
    Clarity.identify(customId, customSessionId, customPageId, friendlyName);
  };

  return {
    trackEvent,
    setTag,
    identify,
  };
}
