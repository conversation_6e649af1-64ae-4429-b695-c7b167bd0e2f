// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { type Metadata } from "next";
import { ReactNode } from "react";

export const metadata: Metadata = {
  title: "Chat with <PERSON><PERSON><PERSON> - Free Deep Research Assistant",
  description: "Start chatting with Deer<PERSON>low's free AI research assistant. Ask questions, get comprehensive answers, and explore knowledge.",
  keywords: [],
  openGraph: {
    title: "Chat with Deer<PERSON>low - Free Deep Research Assistant",
    description: "Start chatting with Deer<PERSON>low's free AI research assistant. Ask questions, get comprehensive answers, and explore knowledge.",
    type: "website",
    url: "/chat",
    siteName: "DeerFlow",
    images: [
      {
        url: "/images/deer-hero.svg",
        width: 1200,
        height: 630,
        alt: "DeerFlow AI Research Assistant",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Chat with DeerF<PERSON> - Free Deep Research Assistant", 
    description: "Start chatting with <PERSON><PERSON><PERSON>'s free AI research assistant for interactive knowledge exploration.",
    images: ["/images/deer-hero.svg"],
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_WEB_URL}/chat`,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function ChatLayout({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <>
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": "DeerFlow AI Research Assistant",
            "description": "Advanced AI-powered research assistant for comprehensive knowledge exploration and deep analysis",
            "url": "https://deerflow.net/chat",
            "applicationCategory": "EducationalApplication",
            "operatingSystem": "Web Browser",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD",
              "availability": "https://schema.org/InStock"
            },
            "provider": {
              "@type": "Organization",
              "name": "DeerFlow",
              "url": "https://deerflow.net"
            },
            "featureList": [
              "Deep thinking AI analysis",
              "Multi-step research planning", 
              "Real-time web search integration",
              "Comprehensive report generation",
              "Interactive chat interface",
              "Mobile responsive design"
            ]
          })
        }}
      />
      {children}
    </>
  );
} 