'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

interface PaymentResult {
  success: boolean;
  message: string;
  orderId?: string;
  credits?: number;
  productName?: string;
  amount?: number;
  currency?: string;
}

export default function CreemPaymentSuccessPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [result, setResult] = useState<PaymentResult | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const processPayment = async () => {
      try {
        // 获取 Creem Return URL 参数
        const checkoutId = searchParams.get('checkout_id');
        const orderId = searchParams.get('order_id');
        const customerId = searchParams.get('customer_id');
        const productId = searchParams.get('product_id');
        const subscriptionId = searchParams.get('subscription_id');
        const signature = searchParams.get('signature');

        console.log('Creem payment return parameters:', {
          checkoutId,
          orderId,
          customerId,
          productId,
          subscriptionId,
          signature: signature?.substring(0, 20) + '...'
        });

        if (!checkoutId || !signature) {
          setResult({
            success: false,
            message: '支付参数不完整，请联系客服'
          });
          return;
        }

        // 调用后端 API 处理支付结果
        const response = await fetch('/api/creem/payment-return', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            checkout_id: checkoutId,
            order_id: orderId,
            customer_id: customerId,
            product_id: productId,
            subscription_id: subscriptionId,
            signature: signature,
          }),
        });

        const data = await response.json();

        if (response.ok) {
          setResult({
            success: true,
            message: '支付成功！积分已添加到您的账户',
            orderId: data.orderId,
            credits: data.credits,
            productName: data.productName,
            amount: data.amount,
            currency: data.currency,
          });
        } else {
          setResult({
            success: false,
            message: data.error || '支付处理失败，请联系客服'
          });
        }

      } catch (error) {
        console.error('Payment processing error:', error);
        setResult({
          success: false,
          message: '处理支付结果时出错，请联系客服'
        });
      } finally {
        setLoading(false);
      }
    };

    processPayment();
  }, [searchParams]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">正在处理支付结果...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {result?.success ? (
              <CheckCircle className="h-16 w-16 text-green-500" />
            ) : (
              <AlertCircle className="h-16 w-16 text-red-500" />
            )}
          </div>
          <CardTitle className={`text-2xl ${result?.success ? 'text-green-600' : 'text-red-600'}`}>
            {result?.success ? '支付成功！' : '支付失败'}
          </CardTitle>
          <CardDescription>
            {result?.success ? '您的支付已成功处理，积分即将到账' : result?.message}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {result?.success && (
            <>
              <div className="space-y-2">
                {result.productName && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">产品</span>
                    <span className="font-medium">{result.productName}</span>
                  </div>
                )}
                
                {result.credits && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">积分数量</span>
                    <Badge variant="secondary">
                      {result.credits.toLocaleString()} 积分
                    </Badge>
                  </div>
                )}
                
                {result.amount && result.currency && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">支付金额</span>
                    <span className="font-medium">
                      {result.currency} {result.amount}
                    </span>
                  </div>
                )}
                
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">支付状态</span>
                  <Badge variant="default" className="bg-green-500">
                    已完成
                  </Badge>
                </div>

                {result.orderId && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">订单号</span>
                    <span className="text-sm font-mono">{result.orderId}</span>
                  </div>
                )}
              </div>
              
              <Separator />
              
              <div className="text-center text-sm text-muted-foreground">
                <p>积分将在几分钟内自动添加到您的账户</p>
                <p className="mt-1">如有问题请联系客服</p>
              </div>
            </>
          )}
          
          <div className="flex flex-col gap-3">
            <Button 
              onClick={() => router.push('/console')}
              className="w-full"
            >
              前往控制台
            </Button>
            
            <Button 
              variant="outline"
              onClick={() => router.push('/pricing')}
              className="w-full"
            >
              继续购买
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
