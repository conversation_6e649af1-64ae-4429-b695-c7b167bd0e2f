import { respErr, respData } from "@/lib/resp";
import { getUserCredits } from "@/services/credit";
import { getUserUuid } from "@/services/user";

export async function POST(req: Request) {
  try {
    // 使用项目标准的认证方式
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      // 返回401状态码用于前端判断
      return Response.json(
        { code: -1, message: "no auth" },
        { status: 401 }
      );
    }

    // 获取用户积分
    const userCredits = await getUserCredits(user_uuid);
    
    // 检查积分是否足够（需要2积分）
    const requiredCredits = 2;
    if (userCredits.left_credits < requiredCredits) {
      // 返回402状态码用于前端判断
      return Response.json(
        { 
          code: -1,
          message: `积分不足，需要${requiredCredits}积分，当前积分：${userCredits.left_credits}`,
          data: { left_credits: userCredits.left_credits }
        },
        { status: 402 }
      );
    }

    // 检查通过
    return respData({ left_credits: userCredits.left_credits });

  } catch (error) {
    console.error("预检查失败:", error);
    return respErr("检查失败");
  }
} 