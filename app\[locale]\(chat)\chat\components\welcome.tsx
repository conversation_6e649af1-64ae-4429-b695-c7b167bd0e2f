// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { motion } from "framer-motion";

import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";

export function Welcome({ className }: { className?: string }) {
  const isMobile = useIsMobile();
  return (
    <motion.div
      className={cn("flex flex-col", className)}
      style={{ transition: "all 0.2s ease-out" }}
      initial={{ opacity: 0, scale: 0.85 }}
      animate={{ opacity: 1, scale: 1 }}
    >
      <h3 className={cn(
        "mb-2 text-center font-medium",
        // 桌面端保持原有样式
        !isMobile && "text-3xl",
        // 移动端使用更小的字体
        isMobile && "text-2xl"
      )}>
        👋 Hello, there!
      </h3>
      <div className={cn(
        "text-muted-foreground text-center",
        // 桌面端保持原有样式
        !isMobile && "px-4 text-lg",
        // 移动端优化样式
        isMobile && "px-2 text-base"
      )}>
        Welcome to 🦌 Deer<PERSON><PERSON>, a deep research assistant built on cutting-edge language models, helps
        you search on web, browse information, and handle complex tasks.
      </div>
    </motion.div>
  );
}
