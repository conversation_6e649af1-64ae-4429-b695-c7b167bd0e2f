import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServiceClient } from '@/models/db';

const supabase = getSupabaseServiceClient();

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const email = searchParams.get('email');
  const token = searchParams.get('token');

  if (!email || !token) {
    return NextResponse.json({ error: 'Missing parameters' }, { status: 400 });
  }

  // 查找用户
  const { data: user, error } = await supabase
    .from('users')
    .select('id, email_verified, verification_token')
    .eq('email', email)
    .eq('verification_token', token)
    .maybeSingle();

  if (!user) {
    return NextResponse.json({ error: 'Invalid or expired verification link' }, { status: 400 });
  }

  if (user.email_verified) {
    return NextResponse.json({ message: 'Email already verified, please sign in directly' });
  }

  // 激活邮箱
  const { error: updateError } = await supabase
    .from('users')
    .update({ email_verified: true, verification_token: null })
    .eq('id', user.id);

  if (updateError) {
    return NextResponse.json({ error: 'Activation failed', detail: updateError.message }, { status: 500 });
  }

  // 可重定向到登录页或返回成功提示
  return NextResponse.json({ message: 'Email verification successful, please return to sign in' });
} 