// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { useEffect, useRef, useState } from "react";

import { env } from "@/lib/env";

import { useReplay } from "../replay";

import { fetchReplayTitle } from "./chat";
import { getConfig, loadConfig } from "./config";

export function useReplayMetadata() {
  const { isReplay } = useReplay();
  const [title, setTitle] = useState<string | null>(null);
  const isLoading = useRef(false);
  const [error, setError] = useState<boolean>(false);
  useEffect(() => {
    if (!isReplay) {
      return;
    }
    if (title || isLoading.current) {
      return;
    }
    isLoading.current = true;
    fetchReplayTitle()
      .then((title) => {
        setError(false);
        setTitle(title ?? null);
        if (title) {
          document.title = `${title} - DeerFlow`;
        }
      })
      .catch(() => {
        setError(true);
        setTitle("Error: the replay is not available.");
        document.title = "DeerFlow";
      })
      .finally(() => {
        isLoading.current = false;
      });
  }, [isLoading, isReplay, title]);
  return { title, isLoading, hasError: error };
}

export function useRAGProvider() {
  const [loading, setLoading] = useState(true);
  const [provider, setProvider] = useState<string | null>(null);

  useEffect(() => {
    if (env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY) {
      setLoading(false);
      return;
    }

    const initProvider = async () => {
      try {
        // 检查配置是否已加载
        if (typeof window !== "undefined" && window.__deerflowConfig) {
          setProvider(window.__deerflowConfig.rag.provider);
          setLoading(false);
          return;
        }

        // 尝试加载配置
        const config = await loadConfig();
        if (typeof window !== "undefined") {
          window.__deerflowConfig = config;
        }
        setProvider(config.rag.provider);
        setLoading(false);
      } catch (error) {
        console.warn("Failed to load RAG config, using default:", error);
        // 提供默认配置
        const defaultConfig = {
          rag: { provider: 'none' },
          models: { basic: [], reasoning: [] }
        };
        if (typeof window !== "undefined") {
          window.__deerflowConfig = defaultConfig;
        }
        setProvider('none');
        setLoading(false);
      }
    };

    initProvider();
  }, []);

  return { provider, loading };
}
