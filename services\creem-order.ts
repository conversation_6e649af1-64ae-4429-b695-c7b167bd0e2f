// services/creem-order.ts
import { getCreemClient } from '@/lib/creem/client';
import { 
  createCreemOrder,
  updateCreemOrder,
  getCreemOrderById,
  getCreemOrderBySessionId,
  updateCreemOrderStatus,
  type CreemOrder
} from '@/models/creem-order';
import type { 
  CreemCheckoutSession,
  CreemWebhookEvent,
  CreemCreateCheckoutParams
} from '@/lib/creem/types';

export class CreemOrderService {
  private creem: ReturnType<typeof getCreemClient>;

  constructor() {
    this.creem = getCreemClient();
  }

  async createCheckoutSession(params: {
    userId: string;
    userEmail: string;
    productId: string;
    productName: string;
    amount: number;
    currency?: string;
    credits: number;
    validMonths: number;
    successUrl?: string;
    cancelUrl?: string;
  }): Promise<CreemCheckoutSession> {
    try {
      // 1. 创建本地订单记录
      const order = await createCreemOrder({
        user_uuid: params.userId,
        user_email: params.userEmail,
        product_id: params.productId,
        product_name: params.productName,
        amount: params.amount,
        currency: params.currency || 'USD',
        status: 'pending',
        payment_provider: 'creem',
        credits: params.credits,
        valid_months: params.validMonths
      });

      // 2. 创建Creem支付会话
      const checkoutRequest: CreemCreateCheckoutParams = {
        product_id: params.productId,
        customer_email: params.userEmail,
        customer_name: '', // 可选
        success_url: params.successUrl || `${process.env.NEXT_PUBLIC_BASE_URL}/payment/success/${order.id}`,
        cancel_url: params.cancelUrl || `${process.env.NEXT_PUBLIC_BASE_URL}/pricing`,
        metadata: {
          order_id: order.id,
          user_id: params.userId
        }
      };

      const session = await this.creem.createCheckoutSession(checkoutRequest);

      // 3. 更新订单记录
      await updateCreemOrder(order.id, {
        creem_session_id: session.id,
        creem_customer_id: session.customer_id
      });

      return session;
    } catch (error) {
      console.error('创建Creem支付会话失败:', error);
      throw new Error(`创建支付会话失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  async handleWebhookEvent(event: CreemWebhookEvent): Promise<void> {
    try {
      switch (event.type) {
        case 'checkout.session.completed':
        case 'payment.succeeded':
          await this.handlePaymentSuccess(event);
          break;
        case 'payment.failed':
          await this.handlePaymentFailed(event);
          break;
        case 'checkout.session.cancelled':
          await this.handlePaymentCancelled(event);
          break;
        default:
          console.log(`未处理的Webhook事件类型: ${event.type}`);
      }
    } catch (error) {
      console.error('处理Webhook事件失败:', error);
      throw error;
    }
  }

  private async handlePaymentSuccess(event: CreemWebhookEvent): Promise<void> {
    const session = event.data.object;
    const orderId = session.metadata?.order_id;

    if (!orderId) {
      throw new Error('订单ID不存在于Webhook元数据中');
    }

    // 1. 更新订单状态
    await updateCreemOrderStatus(orderId, 'paid');

    // 2. 增加用户积分
    const order = await getCreemOrderById(orderId);
    if (order) {
      await this.addUserCredits(order.user_uuid, order.credits, order.id);
    }

    console.log(`支付成功处理完成 - 订单ID: ${orderId}`);
  }

  private async handlePaymentFailed(event: CreemWebhookEvent): Promise<void> {
    const session = event.data.object;
    const orderId = session.metadata?.order_id;

    if (orderId) {
      await updateCreemOrderStatus(orderId, 'failed');
    }

    console.log(`支付失败处理完成 - 订单ID: ${orderId}`);
  }

  private async handlePaymentCancelled(event: CreemWebhookEvent): Promise<void> {
    const session = event.data.object;
    const orderId = session.metadata?.order_id;

    if (orderId) {
      await updateCreemOrderStatus(orderId, 'cancelled');
    }

    console.log(`支付取消处理完成 - 订单ID: ${orderId}`);
  }

  private async addUserCredits(userId: string, credits: number, orderId: string): Promise<void> {
    try {
      // 调用现有的积分服务
      const { addCredits } = await import('@/services/credit');
      await addCredits(userId, credits, `creem_payment_${orderId}`);
      
      console.log(`用户 ${userId} 增加积分 ${credits}，来源订单: ${orderId}`);
    } catch (error) {
      console.error('增加用户积分失败:', error);
      throw error;
    }
  }
} 
