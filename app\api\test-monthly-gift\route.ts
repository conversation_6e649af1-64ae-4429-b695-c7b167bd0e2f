import { respData, respErr } from "@/lib/resp";
import { 
  checkAndGrantMonthlyGift, 
  hasMonthlyGiftThisMonth, 
  getCurrentMonthKey, 
  getCurrentMonthEndTime 
} from "@/services/credit";
import { getUserUuid } from "@/services/user";

export async function POST(req: Request) {
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    const { action } = await req.json();

    switch (action) {
      case "check":
        const hasReceived = await hasMonthlyGiftThisMonth(user_uuid);
        return respData({
          user_uuid,
          has_received: hasReceived,
          current_month: getCurrentMonthKey(),
          month_end_time: getCurrentMonthEndTime()
        });
      
      case "grant":
        await checkAndGrantMonthlyGift(user_uuid);
        return respData({ 
          message: "Monthly gift checked and granted if needed",
          user_uuid 
        });
      
      default:
        return respErr("invalid action");
    }
  } catch (e) {
    console.log("test monthly gift failed:", e);
    return respErr("test failed");
  }
} 