// lib/creem/config.ts
// Creem配置管理

export interface CreemConfig {
  apiUrl: string;
  apiKey: string;
  webhookSecret: string;
  environment: 'test' | 'production';
}

export function getCreemConfig(): CreemConfig {
  const environment = process.env.CREEM_ENVIRONMENT as 'test' | 'production' || 'test';

  const config: CreemConfig = {
    // 使用环境变量中的 API URL，如果没有则根据环境自动选择
    apiUrl: process.env.CREEM_API_URL || (environment === 'test' ? 'https://test-api.creem.io' : 'https://api.creem.io'),
    apiKey: environment === 'test' ? process.env.CREEM_TEST_API_KEY! : process.env.CREEM_PROD_API_KEY!,
    webhookSecret: environment === 'test' ? process.env.CREEM_TEST_WEBHOOK_SECRET! : process.env.CREEM_PROD_WEBHOOK_SECRET!,
    environment
  };

  console.log('Creem Config:', {
    environment,
    apiUrl: config.apiUrl,
    hasApiKey: !!config.apiKey,
    apiKeyPrefix: config.apiKey?.substring(0, 15) + '...',
    hasWebhookSecret: !!config.webhookSecret
  });

  if (!config.apiKey) {
    throw new Error(`Creem API key is not set for ${environment} environment`);
  }

  if (!config.webhookSecret) {
    throw new Error(`Creem webhook secret is not set for ${environment} environment`);
  }

  return config;
}

// 根据你提供的真实 Creem 产品ID
export const CREEM_PRODUCTS = {
  ONETIME: 'prod_1aV0Z7V41S9hTSNo7Q4Lyg',  // 一次性支付5美元
  MONTHLY: 'prod_76bvTM7cFURZ0XrGsj6fzU', // 月度订阅
  YEARLY: 'prod_I7cILnJceIGZUEFl9Vb9L'   // 年度订阅
} as const;

export const CREEM_PRICING = {
  ONETIME: {
    price: 5,
    credits: 50,
    currency: 'USD'
  },
  MONTHLY: {
    price: 7.9,
    credits: 79,
    currency: 'USD'
  },
  YEARLY: {
    price: 79,
    credits: 948,
    currency: 'USD'
  }
} as const;