// lib/creem/config.ts
// Creem配置管理

export interface CreemConfig {
  apiUrl: string;
  apiKey: string;
  webhookSecret: string;
  environment: 'test' | 'production';
}

export function getCreemConfig(): CreemConfig {
  const environment = process.env.CREEM_ENVIRONMENT as 'test' | 'production' || 'test';
  
  const config: CreemConfig = {
    apiUrl: process.env.CREEM_API_URL || 'https://test-api.creem.io',
    apiKey: process.env.CREEM_TEST_API_KEY!,
    webhookSecret: process.env.CREEM_TEST_WEBHOOK_SECRET!,
    environment
  };

  if (!config.apiKey) {
    throw new Error(`Creem API key is not set for ${environment} environment`);
  }

  if (!config.webhookSecret) {
    throw new Error(`Creem webhook secret is not set for ${environment} environment`);
  }

  return config;
}

export const CREEM_PRODUCTS = {
  MONTHLY: 'monthly_credits',
  YEARLY: 'yearly_credits',
  ONETIME: 'onetime_credits'
} as const;

export const CREEM_PRICING = {
  MONTHLY: {
    price: 7.9,
    credits: 79,
    currency: 'USD'
  },
  YEARLY: {
    price: 79,
    credits: 948,
    currency: 'USD'
  },
  ONETIME: {
    price: 5,
    credits: 50,
    currency: 'USD'
  }
} as const;