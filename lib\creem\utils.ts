// lib/creem/utils.ts
import type { CreemWebhookEvent } from './types';

export function validateWebhookSignature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  try {
    const crypto = require('crypto');
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');
    
    return signature === expectedSignature;
  } catch (error) {
    console.error('Webhook签名验证失败:', error);
    return false;
  }
}

export function parseWebhookEvent(payload: string): CreemWebhookEvent {
  try {
    return JSON.parse(payload);
  } catch (error) {
    throw new Error('无效的Webhook事件格式');
  }
}
