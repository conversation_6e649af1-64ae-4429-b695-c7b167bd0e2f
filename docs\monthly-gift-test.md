# 月度积分功能测试文档

## 功能说明
- 每个用户每月可获得10积分
- 积分仅当月有效（当月最后一天23:59:59过期）
- 新用户注册时立即发放
- 老用户登录时检查并发放
- 同一用户同一月只能获得一次

## 测试API

### 1. 检查月度积分状态
```bash
curl -X POST http://localhost:3000/api/test-monthly-gift \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"action": "check"}'
```

### 2. 发放月度积分
```bash
curl -X POST http://localhost:3000/api/test-monthly-gift \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"action": "grant"}'
```

## 测试场景

### 场景1：新用户注册
1. 注册新用户
2. 检查数据库中是否有 `trans_type: 'monthly_gift'` 的记录
3. 验证积分数量为10
4. 验证过期时间为当月最后一天23:59:59

### 场景2：老用户登录
1. 使用现有用户登录
2. 检查是否获得当月积分
3. 再次登录，验证不会重复发放

### 场景3：跨月测试
1. 修改系统时间到下个月
2. 用户登录，应该获得新月份的积分
3. 验证上个月的积分已过期

## 数据库查询

### 查看用户积分记录
```sql
SELECT 
  trans_type,
  credits,
  created_at,
  expired_at
FROM credits 
WHERE user_uuid = 'YOUR_USER_UUID'
ORDER BY created_at DESC;
```

### 查看月度积分记录
```sql
SELECT 
  user_uuid,
  credits,
  created_at,
  expired_at
FROM credits 
WHERE trans_type = 'monthly_gift'
ORDER BY created_at DESC;
```

## 注意事项
1. 时区使用UTC
2. 积分过期时间精确到毫秒
3. 测试完成后删除测试API 