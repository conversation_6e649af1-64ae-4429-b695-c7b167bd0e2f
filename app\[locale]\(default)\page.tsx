import { useMemo } from "react";
import DeerFlowFAQ from "@/components/deerflow/faq-deerflow";
import { Jumbotron } from "@/components/deerflow/landing/components/jumbotron";
import { Ray } from "@/components/deerflow/landing/components/ray";
import { CaseStudySection } from "@/components/deerflow/landing/sections/case-study-section";
import { MultiAgentSection } from "@/components/deerflow/landing/sections/multi-agent-section";
import { getLandingPage } from "@/services/page";

//这段代码作用是多语言的SEO优化
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getLandingPage(locale);

  return (
    <div className="flex flex-col items-center">
      <main className="container flex flex-col items-center justify-center gap-56">
        <Jumbotron />
        <CaseStudySection />
        <MultiAgentSection />
      </main>
      <DeerFlowFAQ />
      <Ray />
    </div>
  );

  /* ShipAny原有内容 - 暂时注释掉，待后续整合
  return (
    <>
      {page.hero && <Hero hero={page.hero} />}
      {page.branding && <Branding section={page.branding} />}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.usage && <Feature3 section={page.usage} />}
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.stats && <Stats section={page.stats} />}
      {page.pricing && <Pricing pricing={page.pricing} />}
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
  */
}