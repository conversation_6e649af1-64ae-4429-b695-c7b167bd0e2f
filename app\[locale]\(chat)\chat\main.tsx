// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

"use client";

import { useMemo, useCallback } from "react";

import type { Resource } from "@/lib/deerflow/core/messages/types";
import { useStore, sendMessage, useMessageIds } from "@/lib/deerflow/core/store";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";

import { ConversationStarter } from "./components/conversation-starter";
import { MessagesBlock } from "./components/messages-block";
import { MobileInputBox } from "./components/mobile-input-box";
import { ResearchBlock } from "./components/research-block";

export default function Main() {
  const openResearchId = useStore((state) => state.openResearchId);
  const responding = useStore((state) => state.responding);
  const messageIds = useMessageIds();
  const messageCount = messageIds.length;
  const isMobile = useIsMobile();
  const doubleColumnMode = useMemo(
    () => openResearchId !== null,
    [openResearchId],
  );
  const handleSend = useCallback(
    async (message: string, options?: { interruptFeedback?: string; resources?: Resource[] }) => {
      try {
        await sendMessage(
          message,
          {
            interruptFeedback: options?.interruptFeedback,
            resources: options?.resources,
          },
        );
      } catch {}
    },
    [],
  );

  const handleCancel = useCallback(() => {
    console.log('handleCancel called');
    const currentController = useStore.getState().currentAbortController;
    if (currentController) {
      currentController.abort();
      useStore.getState().setCurrentAbortController(null);
    }
    // 手动设置responding为false，确保UI状态正确更新
    useStore.setState({ responding: false });
  }, []);

  if (isMobile) {
    // 移动端特殊布局：固定输入框在底部，Research Activities在中间可滚动
    return (
      <div className="flex h-full w-full max-w-full overflow-x-hidden flex-col">
        {/* 中间可滚动内容区域 */}
        <div className="flex-1 overflow-hidden pt-20 pb-40 relative">
          {doubleColumnMode && (
            <ResearchBlock
              className="h-full w-full px-2 rounded-lg shadow-sm overflow-hidden"
              researchId={openResearchId}
            />
          )}
          {!doubleColumnMode && (
            <div className="h-full px-2 relative">
              <MessagesBlock 
                className="h-full w-full rounded-lg" 
                hideInputBox={true}
              />
              {/* 在没有消息时显示ConversationStarter */}
              {!responding && messageCount === 0 && (
                <div className="absolute inset-0 flex items-center justify-center px-2">
                  <ConversationStarter
                    className="w-full max-w-md"
                    onSend={handleSend}
                  />
                </div>
              )}
            </div>
          )}
        </div>
        
        {/* 固定在底部的输入框 - 始终显示以支持随时中断 */}
        <div className="fixed bottom-0 left-0 right-0 bg-app/80 backdrop-blur-md p-2 z-40">
          <div className="w-full max-w-md mx-auto">
            <MobileInputBox onSend={handleSend} onCancel={handleCancel} />
          </div>
        </div>
      </div>
    );
  }

  // 桌面端保持原有布局
  return (
    <div
      className={cn(
        "flex h-full justify-center",
        "w-full px-4 pt-12 pb-4",
        doubleColumnMode && "gap-8",
      )}
    >
      <MessagesBlock
        className={cn(
          "shrink-0 transition-all duration-300 ease-out",
          !doubleColumnMode &&
            `w-[768px] translate-x-[min(max(calc((100vw-538px)*0.75),575px)/2,960px/2)]`,
          doubleColumnMode && `w-[538px]`,
        )}
      />
      <ResearchBlock
        className={cn(
          "transition-all duration-300 ease-out rounded-lg shadow-sm",
          "w-[min(calc((100vw-538px)*0.7),900px)] pb-4",
          !doubleColumnMode && "scale-0 opacity-0",
          doubleColumnMode && "scale-100 opacity-100",
        )}
        researchId={openResearchId}
      />
    </div>
  );
}
