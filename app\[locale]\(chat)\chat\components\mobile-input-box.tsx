// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

"use client";

import { Lightbulb, Send } from "lucide-react";
import { useCallback, useRef, useState } from "react";

import MessageInput from "@/components/deerflow/deer-flow/message-input";
import { Tooltip } from "@/components/deerflow/deer-flow/tooltip";
import { Button } from "@/components/ui/button";
import type { Option, Resource } from "@/lib/deerflow/core/messages";
import { sendMessage, useStore } from "@/lib/deerflow/core/store";
import { cn } from "@/lib/utils";

export function MobileInputBox({ 
  onSend,
  onCancel
}: { 
  onSend?: (message: string, options?: { interruptFeedback?: string; resources?: Array<Resource> }) => Promise<void>;
  onCancel?: () => void;
}) {
  const responding = useStore((state) => state.responding);
  
  // 调试信息
  console.log('MobileInputBox responding state:', responding);
  const [currentPrompt, setCurrentPrompt] = useState("");
  const [feedback, setFeedback] = useState<{ option: Option } | null>(null);
  const [enableDeepThinking, setEnableDeepThinking] = useState(false);
  const inputRef = useRef<{ focus: () => void; submit: () => void; setContent: (content: string) => void }>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const handleSendMessage = useCallback(
    async (message: string, resources?: Array<Resource>) => {
      if (!message.trim()) return;
      
      try {
        if (onSend) {
          // 使用传入的onSend函数
          await onSend(message, {
            interruptFeedback: feedback?.option.value,
            resources: resources,
          });
        } else {
          // 使用默认的sendMessage函数
          const abortController = new AbortController();
          abortControllerRef.current = abortController;
          
          await sendMessage(
            message,
            {
              interruptFeedback: feedback?.option.value,
              resources: resources,
            },
            {
              abortSignal: abortController.signal,
            },
          );
        }
        setFeedback(null);
      } catch (error) {
        console.error("发送消息失败:", error);
      }
    },
    [onSend, feedback],
  );

  const handleCancel = useCallback(() => {
    if (onCancel) {
      // 使用传入的onCancel函数
      onCancel();
    } else {
      // 使用默认的中断逻辑
      abortControllerRef.current?.abort();
      abortControllerRef.current = null;
    }
  }, [onCancel]);

  return (
    <div className="bg-card w-full rounded-lg border p-3 shadow-lg">
      <div className="w-full">
        <MessageInput
          className="min-h-[60px] px-3 py-2"
          ref={inputRef}
          onEnter={handleSendMessage}
          onChange={setCurrentPrompt}
        />
      </div>
      
      <div className="flex items-center justify-between mt-2">
        {/* 左侧功能按钮 */}
        <div className="flex gap-2">
          <Tooltip title="深度思考模式">
            <Button
              className={cn(
                "rounded-xl text-xs px-2 py-1 h-8",
                enableDeepThinking && "!border-brand !text-brand",
              )}
              variant="outline"
              size="sm"
              onClick={() => setEnableDeepThinking(!enableDeepThinking)}
            >
              <Lightbulb size={12} />
            </Button>
          </Tooltip>
        </div>

        {/* 右侧发送/取消按钮 */}
        <div className="flex gap-2">
          {responding ? (
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              className="rounded-xl text-xs px-3 py-1 h-8"
            >
              Cancel
            </Button>
          ) : (
            <Button
              size="sm"
              onClick={() => inputRef.current?.submit()}
              disabled={!currentPrompt.trim()}
              className="rounded-xl px-2 py-1 h-8"
            >
              <Send size={14} />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
} 