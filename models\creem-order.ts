// models/creem-order.ts
import { getSupabaseClient } from '@/models/db';
import { getSnowId } from '@/lib/hash';

export interface CreemOrder {
  id: string;
  order_no: string;
  user_uuid: string;
  user_email: string;
  product_type: string; // 修改：使用新的字段名
  product_name: string;
  amount: number;
  currency: string;
  status: 'pending' | 'paid' | 'failed' | 'cancelled';
  payment_provider: 'creem';
  creem_session_id?: string;
  creem_customer_id?: string;
  creem_product_id?: string; // 这个保留，存储真实的 Creem 产品ID
  failure_reason?: string;
  paid_at?: string;
  created_at: string;
  updated_at: string;
  credits: number;
  valid_months: number;
}

export interface CreateCreemOrderData {
  user_uuid: string;
  user_email: string;
  product_type: string; // 修改：使用新的字段名
  product_name: string;
  amount: number;
  currency: string;
  status: 'pending' | 'paid' | 'failed' | 'cancelled';
  payment_provider: 'creem';
  creem_session_id?: string;
  creem_customer_id?: string;
  creem_product_id?: string; // 这个保留，存储真实的 Creem 产品ID
  credits: number;
  valid_months: number;
}

export async function createCreemOrder(data: CreateCreemOrderData): Promise<CreemOrder> {
  const supabase = getSupabaseClient();
  
  const insertData = {
    order_no: getSnowId(), // 修正：使用项目统一的ID生成方法
    user_uuid: data.user_uuid,
    user_email: data.user_email,
    product_type: data.product_type, // 修改：使用新的字段名
    product_name: data.product_name,
    amount: data.amount,
    currency: data.currency,
    status: data.status,
    payment_provider: data.payment_provider,
    creem_session_id: data.creem_session_id,
    creem_customer_id: data.creem_customer_id,
    creem_product_id: data.creem_product_id,
    credits: data.credits,
    valid_months: data.valid_months,
    created_at: new Date().toISOString(),
  };

  const { data: order, error } = await supabase
    .from('orders')
    .insert(insertData)
    .select()
    .single();

  if (error) {
    throw new Error(`创建订单失败: ${error.message}`);
  }

  return order;
}

export async function updateCreemOrder(orderId: string, updates: Partial<CreemOrder>): Promise<void> {
  const supabase = getSupabaseClient();
  
  const { error } = await supabase
    .from('orders')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', orderId)
    .eq('payment_provider', 'creem');

  if (error) {
    throw new Error(`更新订单失败: ${error.message}`);
  }
}

export async function updateCreemOrderStatus(orderId: string, status: CreemOrder['status']): Promise<void> {
  const supabase = getSupabaseClient();

  const updateData: any = {
    status,
    updated_at: new Date().toISOString()
  };

  if (status === 'paid') {
    updateData.paid_at = new Date().toISOString();
  }

  const { error } = await supabase
    .from('orders')
    .update(updateData)
    .eq('id', orderId)
    .eq('payment_provider', 'creem');

  if (error) {
    throw new Error(`更新订单状态失败: ${error.message}`);
  }
}

export async function getCreemOrderById(orderId: string): Promise<CreemOrder | null> {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .eq('id', orderId)
    .eq('payment_provider', 'creem')
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // 没有找到记录
      return null;
    }
    throw new Error(`获取订单失败: ${error.message}`);
  }

  return data;
}

export async function getCreemOrderBySessionId(sessionId: string): Promise<CreemOrder | null> {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .eq('creem_session_id', sessionId)
    .eq('payment_provider', 'creem')
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // 没有找到记录
      return null;
    }
    throw new Error(`获取订单失败: ${error.message}`);
  }

  return data;
}
