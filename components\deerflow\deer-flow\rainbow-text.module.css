.animated {
  background: linear-gradient(
    to right,
    rgb(from var(--card-foreground) r g b / 0.3) 15%,
    rgb(from var(--card-foreground) r g b / 0.75) 35%,
    rgb(from var(--card-foreground) r g b / 0.75) 65%,
    rgb(from var(--card-foreground) r g b / 0.3) 85%
  );
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  background-size: 500% auto;
  animation: textShine 2s ease-in-out infinite alternate;
}

@keyframes textShine {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}
