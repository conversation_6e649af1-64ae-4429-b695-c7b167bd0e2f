// app/api/creem/payment-return/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCreemConfig } from '@/lib/creem/config';
import { updateCreemOrder, getCreemOrderBySessionId } from '@/models/creem-order';
import { increaseCredits, CreditsTransType } from '@/services/credit';
import crypto from 'crypto';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      checkout_id,
      order_id,
      customer_id,
      product_id,
      subscription_id,
      signature
    } = body;

    console.log('Processing Creem payment return:', {
      checkout_id,
      order_id,
      customer_id,
      product_id,
      subscription_id,
      signature: signature?.substring(0, 20) + '...'
    });

    // 验证必需参数
    if (!checkout_id || !signature) {
      return NextResponse.json(
        { error: '缺少必需的支付参数' },
        { status: 400 }
      );
    }

    // 验证签名
    const config = getCreemConfig();
    const isValidSignature = verifyCreemSignature(
      { checkout_id, order_id, customer_id, product_id, subscription_id },
      signature,
      config.apiKey
    );

    if (!isValidSignature) {
      console.error('Invalid Creem signature');
      return NextResponse.json(
        { error: '签名验证失败' },
        { status: 400 }
      );
    }

    // 根据 checkout_id 查找订单
    const creemOrder = await getCreemOrderBySessionId(checkout_id);
    if (!creemOrder) {
      console.error('Order not found for checkout_id:', checkout_id);
      return NextResponse.json(
        { error: '订单未找到' },
        { status: 404 }
      );
    }

    // 检查订单是否已经处理过
    if (creemOrder.status === 'paid') {
      console.log('Order already processed:', creemOrder.id);
      return NextResponse.json({
        success: true,
        message: '订单已处理',
        orderId: creemOrder.id,
        orderNo: creemOrder.order_no,
        credits: creemOrder.credits,
        productName: getProductName(product_id),
        amount: creemOrder.amount,
        currency: creemOrder.currency,
      });
    }

    // 更新订单状态
    await updateCreemOrder(creemOrder.id, {
      status: 'paid',
      paid_at: new Date().toISOString(),
      paid_email: creemOrder.user_email, // 添加 paid_email 字段用于订单页面显示
    });

    // 给用户添加积分
    if (creemOrder.credits > 0) {
      // 根据订单的 valid_months 计算积分过期时间
      const currentDate = new Date();
      const expiredDate = new Date(currentDate);
      expiredDate.setMonth(currentDate.getMonth() + creemOrder.valid_months);
      const expired_at = expiredDate.toISOString();

      await increaseCredits({
        user_uuid: creemOrder.user_uuid,
        trans_type: CreditsTransType.OrderPay,
        credits: creemOrder.credits,
        order_no: creemOrder.order_no,
        expired_at: expired_at,
      });
      console.log(`Added ${creemOrder.credits} credits to user ${creemOrder.user_uuid}, expires at: ${expired_at}`);
    }

    return NextResponse.json({
      success: true,
      message: '支付处理成功',
      orderId: creemOrder.id,
      orderNo: creemOrder.order_no,
      credits: creemOrder.credits,
      productName: getProductName(product_id),
      amount: creemOrder.amount,
      currency: creemOrder.currency,
    });

  } catch (error) {
    console.error('Payment return processing error:', error);
    return NextResponse.json(
      { error: '支付处理失败' },
      { status: 500 }
    );
  }
}

function verifyCreemSignature(
  params: Record<string, string | null>,
  signature: string,
  apiKey: string
): boolean {
  try {
    // 根据 Creem 官方文档的签名方法
    // 1. 过滤掉空值参数
    const filteredParams = Object.entries(params)
      .filter(([, value]) => value !== null && value !== undefined && value !== '');

    // 2. 按照参数名字母顺序排序
    const sortedParams = filteredParams.sort(([a], [b]) => a.localeCompare(b));

    // 3. 构建签名字符串
    const paramString = sortedParams
      .map(([key, value]) => `${key}=${value}`)
      .join('|');

    const data = `${paramString}|salt=${apiKey}`;

    console.log('Signature verification data:', data);
    console.log('Sorted params:', sortedParams);

    // 使用 SHA256 生成签名
    const expectedSignature = crypto
      .createHash('sha256')
      .update(data)
      .digest('hex');

    console.log('Expected signature:', expectedSignature);
    console.log('Received signature:', signature);

    // 如果排序后的签名匹配，返回 true
    if (signature === expectedSignature) {
      return true;
    }

    // 如果排序后不匹配，尝试不排序的方式（原始顺序）
    console.log('Trying unsorted signature verification...');
    const unsortedData = filteredParams
      .map(([key, value]) => `${key}=${value}`)
      .concat(`salt=${apiKey}`)
      .join('|');

    console.log('Unsorted signature data:', unsortedData);

    const unsortedSignature = crypto
      .createHash('sha256')
      .update(unsortedData)
      .digest('hex');

    console.log('Unsorted expected signature:', unsortedSignature);

    return signature === unsortedSignature;
  } catch (error) {
    console.error('Signature verification error:', error);
    return false;
  }
}

function getProductName(productId: string | null): string {
  if (!productId) return '未知产品';
  
  // 这里可以根据实际的产品 ID 映射返回产品名称
  const productMap: Record<string, string> = {
    'prod_1aV0Z7V41S9hTSNo7Q4Lyg': 'test-Deerflow',
    // 添加更多产品映射
  };
  
  return productMap[productId] || productId;
}
