// app/api/creem/payment-return/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCreemConfig } from '@/lib/creem/config';
import { updateCreemOrder, getCreemOrderBySessionId } from '@/models/creem-order';
import { increaseCredits, CreditsTransType } from '@/services/credit';
import crypto from 'crypto';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      checkout_id,
      order_id,
      customer_id,
      product_id,
      subscription_id,
      signature
    } = body;

    console.log('Processing Creem payment return:', {
      checkout_id,
      order_id,
      customer_id,
      product_id,
      subscription_id,
      signature: signature?.substring(0, 20) + '...'
    });

    // 验证必需参数
    if (!checkout_id || !signature) {
      return NextResponse.json(
        { error: '缺少必需的支付参数' },
        { status: 400 }
      );
    }

    // 验证签名
    const config = getCreemConfig();
    const isValidSignature = verifyCreemSignature(
      { checkout_id, order_id, customer_id, product_id, subscription_id },
      signature,
      config.webhookSecret  // 使用 webhookSecret 而不是 apiKey
    );

    if (!isValidSignature) {
      console.error('Invalid Creem signature');
      return NextResponse.json(
        { error: '签名验证失败' },
        { status: 400 }
      );
    }

    // 根据 checkout_id 查找订单
    const creemOrder = await getCreemOrderBySessionId(checkout_id);
    if (!creemOrder) {
      console.error('Order not found for checkout_id:', checkout_id);
      return NextResponse.json(
        { error: '订单未找到' },
        { status: 404 }
      );
    }

    // 检查订单是否已经处理过
    if (creemOrder.status === 'paid') {
      console.log('Order already processed:', creemOrder.id);
      return NextResponse.json({
        success: true,
        message: '订单已处理',
        orderId: creemOrder.id,
        orderNo: creemOrder.order_no,
        credits: creemOrder.credits,
        productName: getProductName(product_id),
        amount: creemOrder.amount,
        currency: creemOrder.currency,
      });
    }

    // 更新订单状态
    await updateCreemOrder(creemOrder.id, {
      status: 'paid',
      paid_at: new Date().toISOString(),
      paid_email: creemOrder.user_email, // 添加 paid_email 字段用于订单页面显示
    });

    // 给用户添加积分
    if (creemOrder.credits > 0) {
      // 根据订单的 valid_months 计算积分过期时间
      const currentDate = new Date();
      const expiredDate = new Date(currentDate);
      expiredDate.setMonth(currentDate.getMonth() + creemOrder.valid_months);
      const expired_at = expiredDate.toISOString();

      await increaseCredits({
        user_uuid: creemOrder.user_uuid,
        trans_type: CreditsTransType.OrderPay,
        credits: creemOrder.credits,
        order_no: creemOrder.order_no,
        expired_at: expired_at,
      });
      console.log(`Added ${creemOrder.credits} credits to user ${creemOrder.user_uuid}, expires at: ${expired_at}`);
    }

    return NextResponse.json({
      success: true,
      message: '支付处理成功',
      orderId: creemOrder.id,
      orderNo: creemOrder.order_no,
      credits: creemOrder.credits,
      productName: getProductName(product_id),
      amount: creemOrder.amount,
      currency: creemOrder.currency,
    });

  } catch (error) {
    console.error('Payment return processing error:', error);
    return NextResponse.json(
      { error: '支付处理失败' },
      { status: 500 }
    );
  }
}

function verifyCreemSignature(
  params: Record<string, string | null>,
  signature: string,
  webhookSecret: string
): boolean {
  try {
    console.log('=== Creem Signature Verification Debug ===');
    console.log('API Key prefix:', apiKey.substring(0, 20) + '...');
    console.log('Raw params:', params);

    // 方法1: 按照官方文档的方法（当前方法）
    const filteredEntries = Object.entries(params)
      .filter(([, value]) => value !== null && value !== undefined && value !== '');

    console.log('Filtered entries:', filteredEntries);

    const data1 = filteredEntries
      .map(([key, value]) => `${key}=${value}`)
      .concat(`salt=${apiKey}`)
      .join('|');

    console.log('Method 1 - Original order data:', data1);
    const signature1 = crypto.createHash('sha256').update(data1).digest('hex');
    console.log('Method 1 - Expected signature:', signature1);

    if (signature === signature1) {
      console.log('✅ Method 1 SUCCESS');
      return true;
    }

    // 方法2: 尝试按字母顺序排序
    const data2 = filteredEntries
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}=${value}`)
      .concat(`salt=${apiKey}`)
      .join('|');

    console.log('Method 2 - Sorted data:', data2);
    const signature2 = crypto.createHash('sha256').update(data2).digest('hex');
    console.log('Method 2 - Expected signature:', signature2);

    if (signature === signature2) {
      console.log('✅ Method 2 SUCCESS');
      return true;
    }

    // 方法3: 尝试特定的参数顺序（基于 URL 参数顺序）
    const orderedKeys = ['checkout_id', 'order_id', 'customer_id', 'product_id', 'subscription_id'];
    const orderedEntries = orderedKeys
      .filter(key => params[key] && params[key] !== null && params[key] !== '')
      .map(key => [key, params[key]]);

    const data3 = orderedEntries
      .map(([key, value]) => `${key}=${value}`)
      .concat(`salt=${apiKey}`)
      .join('|');

    console.log('Method 3 - URL order data:', data3);
    const signature3 = crypto.createHash('sha256').update(data3).digest('hex');
    console.log('Method 3 - Expected signature:', signature3);

    if (signature === signature3) {
      console.log('✅ Method 3 SUCCESS');
      return true;
    }

    // 方法4: 完全按照生成代码的逻辑（不过滤空值，包含 null/undefined）
    const data4 = Object.entries(params)
      .map(([key, value]) => `${key}=${value}`)
      .concat(`salt=${apiKey}`)
      .join('|');

    console.log('Method 4 - No filtering (like generation code):', data4);
    const signature4 = crypto.createHash('sha256').update(data4).digest('hex');
    console.log('Method 4 - Expected signature:', signature4);

    if (signature === signature4) {
      console.log('✅ Method 4 SUCCESS (no filtering)');
      return true;
    }

    console.log('❌ All methods failed');
    console.log('Received signature:', signature);
    return false;

  } catch (error) {
    console.error('Signature verification error:', error);
    return false;
  }
}

function getProductName(productId: string | null): string {
  if (!productId) return '未知产品';
  
  // 这里可以根据实际的产品 ID 映射返回产品名称
  const productMap: Record<string, string> = {
    'prod_1aV0Z7V41S9hTSNo7Q4Lyg': 'test-Deerflow',
    // 添加更多产品映射
  };
  
  return productMap[productId] || productId;
}
