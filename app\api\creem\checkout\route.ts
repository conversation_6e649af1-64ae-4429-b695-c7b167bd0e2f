// app/api/creem/checkout/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { getCreemClient } from '@/lib/creem/client';
import { CREEM_PRODUCTS, CREEM_PRICING } from '@/lib/creem/config';
import { createCreemOrder, updateCreemOrder } from '@/models/creem-order';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { productType, userEmail, userName } = body;

    console.log('Creem checkout request:', { productType, userEmail, userName });

    // 验证用户登录状态
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '用户未登录' },
        { status: 401 }
      );
    }

    console.log('User session:', {
      email: session.user.email,
      id: session.user.id,
      name: session.user.name
    });

    // 验证产品类型
    if (!productType || !CREEM_PRODUCTS[productType as keyof typeof CREEM_PRODUCTS]) {
      console.log('Invalid product type:', productType);
      return NextResponse.json(
        { error: '无效的产品类型' },
        { status: 400 }
      );
    }

    // 获取产品信息
    const productId = CREEM_PRODUCTS[productType as keyof typeof CREEM_PRODUCTS];
    const pricing = CREEM_PRICING[productType as keyof typeof CREEM_PRICING];

    console.log('Product info:', { productId, pricing });

    // 创建订单
    const order = await createCreemOrder({
      user_uuid: session.user.id || session.user.email,
      user_email: session.user.email,
      product_id: productId,
      product_name: `${productType} Credits`,
      amount: pricing.price,
      currency: pricing.currency,
      status: 'pending',
      payment_provider: 'creem',
      creem_product_id: productId,
      credits: pricing.credits,
      valid_months: productType === 'ONETIME' ? 3 : (productType === 'MONTHLY' ? 1 : 12),
    });
    const orderId = order.id;

    // 创建Creem结账会话
    const creemClient = getCreemClient();

    const checkoutParams = {
      product_id: productId,
      customer_email: userEmail || session.user.email,
      customer_name: userName || session.user.name || '',
      success_url: `${process.env.NEXT_PUBLIC_WEB_URL}/payment/success/${orderId}`,
      cancel_url: `${process.env.NEXT_PUBLIC_WEB_URL}/pricing`,
      metadata: {
        order_id: orderId,
        user_id: session.user.id || session.user.email,
        product_type: productType,
        credits: pricing.credits.toString(),
      },
    };

    console.log('Creating Creem checkout session with params:', checkoutParams);

    const checkoutSession = await creemClient.createCheckoutSession(checkoutParams);

    // 更新订单信息
    await updateCreemOrder(orderId, {
      creem_session_id: checkoutSession.id,
      creem_customer_id: checkoutSession.customer_id,
    });

    return NextResponse.json({
      success: true,
      data: {
        checkout_url: checkoutSession.url,
        session_id: checkoutSession.id,
        order_id: orderId,
      },
    });

  } catch (error) {
    console.error('Creem checkout error:', error);
    return NextResponse.json(
      { error: '创建支付会话失败' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('session_id');

    if (!sessionId) {
      return NextResponse.json(
        { error: '缺少会话ID' },
        { status: 400 }
      );
    }

    const creemClient = getCreemClient();
    const session = await creemClient.retrieveCheckoutSession(sessionId);

    return NextResponse.json({
      success: true,
      data: session,
    });

  } catch (error) {
    console.error('Retrieve checkout session error:', error);
    return NextResponse.json(
      { error: '获取支付会话失败' },
      { status: 500 }
    );
  }
} 