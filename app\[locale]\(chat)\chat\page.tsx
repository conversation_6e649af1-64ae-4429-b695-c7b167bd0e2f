// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

"use client";

import "@/app/prosemirror.css";

import { GithubOutlined } from "@ant-design/icons";
import { MessageSquarePlus } from "lucide-react";
import dynamic from "next/dynamic";
import Link from "next/link";
import { Suspense, useCallback } from "react";

import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";

import { Logo } from "@/components/deerflow/deer-flow/logo";
// import { ThemeToggle } from "@/components/deerflow/deer-flow/theme-toggle";
import { Tooltip } from "@/components/deerflow/deer-flow/tooltip";
import { SettingsDialog } from "@/components/deerflow/settings/dialogs/settings-dialog";

const Main = dynamic(() => import("./main"), {
  ssr: false,
  loading: () => (
    <div className="flex h-full w-full items-center justify-center">
      Loading DeerFlow...
    </div>
  ),
});

export default function HomePage() {
  const isMobile = useIsMobile();


  const handleNewConversation = useCallback(() => {
    // 跳转到chat页面并刷新页面，确保完全重置聊天状态
    window.location.href = '/chat';
  }, []);

  return (
    <div className={cn(
      "flex h-screen justify-center overscroll-none bg-app",
      // 桌面端使用w-screen以支持复杂的居中计算
      !isMobile && "w-screen",
      // 移动端使用w-full并添加overflow控制
      isMobile && "w-full max-w-full overflow-x-hidden"
    )}>
      <header className={cn(
        "fixed top-0 left-0 flex w-full max-w-full items-center justify-between overflow-x-hidden",
        // 添加背景、毛玻璃效果和层级，移除下边框
        "bg-app/80 backdrop-blur-md z-50",
        // 桌面端保持原有样式
        !isMobile && "h-12 px-4",
        // 移动端增加高度，减少内边距
        isMobile && "h-16 px-2",
      )}>
        <Logo />
        <div className="flex items-center">
          <Tooltip title="New Chat">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={handleNewConversation}
              className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
            >
              <MessageSquarePlus size={18} />
            </Button>
          </Tooltip>
          <Tooltip title="Star DeerFlow on GitHub">
            <Button variant="ghost" size="icon" asChild className="hidden">
              <Link
                href="https://github.com/bytedance/deer-flow"
                target="_blank"
              >
                <GithubOutlined />
              </Link>
            </Button>
          </Tooltip>
          {/* <ThemeToggle /> */}
          <Suspense>
            <SettingsDialog />
          </Suspense>
        </div>
      </header>
      <Main />
    </div>
  );
}
