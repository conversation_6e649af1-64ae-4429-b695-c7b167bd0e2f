-- Creem 支付字段冲突解决方案
-- 解决 product_id 字段冲突问题

-- 1. 重命名现有字段以避免冲突
-- 将 product_id 重命名为 product_type（表示内部产品类型）
ALTER TABLE orders RENAME COLUMN product_id TO product_type;

-- 将 creem_product_id 重命名为 creem_internal_product_id（避免混淆）
ALTER TABLE orders RENAME COLUMN creem_product_id TO creem_internal_product_id;

-- 2. 添加真正的 Creem 产品ID字段
ALTER TABLE orders ADD COLUMN creem_product_id VARCHAR(255);

-- 3. 确保支付提供商字段存在（如果不存在则添加）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'orders' AND column_name = 'payment_provider') THEN
        ALTER TABLE orders ADD COLUMN payment_provider VARCHAR(50) DEFAULT 'creem';
    END IF;
END $$;

-- 4. 确保其他 Creem 字段存在
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'orders' AND column_name = 'creem_session_id') THEN
        ALTER TABLE orders ADD COLUMN creem_session_id VARCHAR(255);
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'orders' AND column_name = 'creem_customer_id') THEN
        ALTER TABLE orders ADD COLUMN creem_customer_id VARCHAR(255);
    END IF;
END $$;

-- 5. 创建索引提高查询性能
CREATE INDEX IF NOT EXISTS idx_orders_payment_provider ON orders(payment_provider);
CREATE INDEX IF NOT EXISTS idx_orders_product_type ON orders(product_type);
CREATE INDEX IF NOT EXISTS idx_orders_creem_session_id ON orders(creem_session_id);
CREATE INDEX IF NOT EXISTS idx_orders_creem_customer_id ON orders(creem_customer_id);
CREATE INDEX IF NOT EXISTS idx_orders_creem_product_id ON orders(creem_product_id);