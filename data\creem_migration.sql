CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    order_no VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(), -- 建议添加默认值
    updated_at TIMESTAMPTZ DEFAULT NOW(), -- 建议添加此字段
    user_uuid VARCHAR(255) NOT NULL DEFAULT '',
    user_email VARCHAR(255) NOT NULL DEFAULT '',
    amount INT NOT NULL,
    interval VARCHAR(50),
    expired_at TIMESTAMPTZ,
    status VARCHAR(50) NOT NULL,
    stripe_session_id VARCHAR(255),
    credits INT NOT NULL,
    currency VARCHAR(50),
    sub_id VARCHAR(255),
    sub_interval_count INT,
    sub_cycle_anchor INT,
    sub_period_end INT,
    sub_period_start INT,
    sub_times INT,
    product_type VARCHAR(255), -- ✅ 很好！避免了与 Creem product_id 冲突
    product_name VARCHAR(255),
    valid_months INT,
    order_detail TEXT,
    paid_at TIMESTAMPTZ,
    paid_email VARCHAR(255),
    paid_detail TEXT,
    payment_provider VARCHAR(50) DEFAULT 'creem' NOT NULL,
    creem_product_id VARCHAR(255), -- ✅ 完美！存储 Creem 真实产品ID
    creem_session_id VARCHAR(255), -- ✅ Creem 支付会话ID
    creem_customer_id VARCHAR(255), -- ✅ Creem 客户ID
    failure_reason TEXT -- 建议添加：支付失败原因
);

