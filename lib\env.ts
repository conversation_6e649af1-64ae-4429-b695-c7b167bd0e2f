// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

// 简化的环境变量配置，兼容DeerFlow组件
// 避免引入@t3-oss/env-nextjs以防止依赖冲突

// 声明process类型以解决TypeScript错误
declare const process: {
  env: {
    [key: string]: string | undefined;
    NODE_ENV?: string;
    NEXT_PUBLIC_API_URL?: string;
    NEXT_PUBLIC_STATIC_WEBSITE_ONLY?: string;
    AMPLITUDE_API_KEY?: string;
    GITHUB_OAUTH_TOKEN?: string;
  };
};

export const env = {
  // 客户端环境变量
  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || "",
  NEXT_PUBLIC_STATIC_WEBSITE_ONLY: process.env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY === "true",
  
  // 服务端环境变量（如果需要的话）
  NODE_ENV: process.env.NODE_ENV || "development",
  AMPLITUDE_API_KEY: process.env.AMPLITUDE_API_KEY || "",
  GITHUB_OAUTH_TOKEN: process.env.GITHUB_OAUTH_TOKEN || "",
}; 