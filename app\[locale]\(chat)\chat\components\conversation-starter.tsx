// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { useIsMobile } from "@/hooks/use-mobile";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

import type { Resource } from "@/lib/deerflow/core/messages";
import { Welcome } from "./welcome";

const questions = [
  "How many times taller is the Eiffel Tower than the tallest building in the world?",
  "How many years does an average Tesla battery last compared to a gasoline engine?",
  "How many liters of water are required to produce 1 kg of beef?",
  "How many times faster is the speed of light compared to the speed of sound?",
];

export function ConversationStarter({
  className,
  onSend,
}: {
  className?: string;
  onSend?: (
    message: string,
    options?: {
      interruptFeedback?: string;
      resources?: Array<Resource>;
    },
  ) => void;
}) {
  const isMobile = useIsMobile();

  return (
    <div className={cn("flex flex-col items-center", className)}>
      {/* 桌面端使用fixed定位，移动端使用相对定位 */}
      <div className={cn(
        "flex items-center justify-center",
        // 桌面端保持原有的fixed定位
        !isMobile && "pointer-events-none fixed inset-0",
        // 移动端使用相对定位
        isMobile && "w-full mb-8"
      )}>
        <Welcome 
          className={cn(
            // 桌面端保持原有样式
            !isMobile && "pointer-events-auto mb-15 -translate-y-24 w-[90%] md:w-[75%]",
            // 移动端使用全宽且不需要transform
            isMobile && "w-full",
          )} 
        />
      </div>
      <ul className={cn(
        "flex flex-wrap",
        // 桌面端保持原有样式
        !isMobile && "mt-4 md:mt-0",
        // 移动端调整间距
        isMobile && "mt-2",
      )}>
        {questions.map((question, index) => (
          <motion.li
            key={question}
            className={cn(
              "flex shrink-0 active:scale-105",
              // 桌面端保持原有padding，移动端减少padding
              !isMobile && "p-2",
              isMobile && "p-1",
              // 桌面端：4个问题都显示为半宽，移动端：只显示第1个问题且为全宽
              index === 0 ? "w-full md:w-1/2" : "hidden md:flex md:w-1/2"
            )}
            style={{ transition: "all 0.2s ease-out" }}
            initial={{ opacity: 0, y: 24 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{
              duration: 0.2,
              delay: index * 0.1 + 0.5,
              ease: "easeOut",
            }}
          >
            <div
              className={cn(
                "bg-card text-muted-foreground cursor-pointer rounded-2xl border opacity-75 transition-all duration-300 hover:opacity-100 hover:shadow-md w-full",
                // 确保文字自动换行和自适应高度
                "whitespace-normal break-words min-h-[3rem]",
                // 桌面端和移动端的内边距和文字对齐
                !isMobile && "px-4 py-4 text-center md:text-left",
                isMobile && "px-3 py-3 text-center text-sm leading-relaxed"
              )}
              onClick={() => {
                onSend?.(question);
              }}
            >
              {question}
            </div>
          </motion.li>
        ))}
      </ul>
    </div>
  );
}
