// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { MagicWandIcon } from "@radix-ui/react-icons";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowUp, Lightbulb, X } from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import { Detective } from "@/components/deerflow/deer-flow/icons/detective";
import MessageInput, {
  type MessageInputRef,
} from "@/components/deerflow/deer-flow/message-input";
import { ReportStyleDialog } from "@/components/deerflow/deer-flow/report-style-dialog";
import { Tooltip } from "@/components/deerflow/deer-flow/tooltip";
import { BorderBeam } from "@/components/deerflow/magicui/border-beam";
import { Button } from "@/components/ui/button";
import { enhancePrompt } from "@/lib/deerflow/core/api";
import { getConfig, loadConfig } from "@/lib/deerflow/core/api/config";
import type { Option, Resource } from "@/lib/deerflow/core/messages";
import {
  setEnableDeepThinking,
  setEnableBackgroundInvestigation,
  useSettingsStore,
} from "@/lib/deerflow/core/store";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";

export function InputBox({
  className,
  responding,
  feedback,
  onSend,
  onCancel,
  onRemoveFeedback,
}: {
  className?: string;
  size?: "large" | "normal";
  responding?: boolean;
  feedback?: { option: Option } | null;
  onSend?: (
    message: string,
    options?: {
      interruptFeedback?: string;
      resources?: Array<Resource>;
    },
  ) => void;
  onCancel?: () => void;
  onRemoveFeedback?: () => void;
}) {
  const enableDeepThinking = useSettingsStore(
    (state) => state.general.enableDeepThinking,
  );
  const backgroundInvestigation = useSettingsStore(
    (state) => state.general.enableBackgroundInvestigation,
  );
  
  // 配置加载状态
  const [configLoaded, setConfigLoaded] = useState(false);
  const [reasoningModel, setReasoningModel] = useState<string | undefined>();
  
  const reportStyle = useSettingsStore((state) => state.general.reportStyle);

  // 加载配置
  useEffect(() => {
    const initConfig = async () => {
      try {
        // 检查配置是否已加载
        if (typeof window !== "undefined" && window.__deerflowConfig) {
          setReasoningModel(window.__deerflowConfig.models.reasoning?.[0]);
          setConfigLoaded(true);
          return;
        }
        
        // 尝试加载配置
        const config = await loadConfig();
        if (typeof window !== "undefined") {
          window.__deerflowConfig = config;
          setReasoningModel(config.models.reasoning?.[0]);
        }
        setConfigLoaded(true);
      } catch (error) {
        console.warn("Failed to load config, using default:", error);
        // 提供默认配置
        const defaultConfig = {
          rag: { provider: 'none' },
          models: { basic: [], reasoning: [] }
        };
        if (typeof window !== "undefined") {
          window.__deerflowConfig = defaultConfig;
        }
        setReasoningModel(undefined);
        setConfigLoaded(true);
      }
    };
    
    if (!configLoaded) {
      initConfig();
    }
  }, [configLoaded]);
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<MessageInputRef>(null);
  const feedbackRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();

  // Enhancement state
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [isEnhanceAnimating, setIsEnhanceAnimating] = useState(false);
  const [currentPrompt, setCurrentPrompt] = useState("");

  const handleSendMessage = useCallback(
    (message: string, resources: Array<Resource>) => {
      if (responding) {
        onCancel?.();
      } else {
        if (message.trim() === "") {
          return;
        }
        if (onSend) {
          onSend(message, {
            interruptFeedback: feedback?.option.value,
            resources,
          });
          onRemoveFeedback?.();
          // Clear enhancement animation after sending
          setIsEnhanceAnimating(false);
        }
      }
    },
    [responding, onCancel, onSend, feedback, onRemoveFeedback],
  );

  const handleEnhancePrompt = useCallback(async () => {
    if (currentPrompt.trim() === "" || isEnhancing) {
      return;
    }

    setIsEnhancing(true);
    setIsEnhanceAnimating(true);

    try {
      const enhancedPrompt = await enhancePrompt({
        prompt: currentPrompt,
        report_style: reportStyle.toUpperCase(),
      });

      // Add a small delay for better UX
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Update the input with the enhanced prompt with animation
      if (inputRef.current) {
        inputRef.current.setContent(enhancedPrompt);
        setCurrentPrompt(enhancedPrompt);
      }

      // Keep animation for a bit longer to show the effect
      setTimeout(() => {
        setIsEnhanceAnimating(false);
      }, 1000);
    } catch (error) {
      console.error("Failed to enhance prompt:", error);
      setIsEnhanceAnimating(false);
      // Could add toast notification here
    } finally {
      setIsEnhancing(false);
    }
  }, [currentPrompt, isEnhancing, reportStyle]);

  return (
    <div
      className={cn(
        "bg-card relative flex h-full w-full max-w-full flex-col rounded-[24px] border overflow-hidden",
        // 移动端不使用全宽，留出边距
        isMobile && "w-[95%] mx-auto",
        className,
      )}
      ref={containerRef}
    >
      <div className="w-full">
        <AnimatePresence>
          {feedback && (
            <motion.div
              ref={feedbackRef}
              className={cn(
                "bg-background border-brand absolute top-0 left-0 flex items-center justify-center gap-1 rounded-2xl border px-2 py-0.5",
                // 桌面端保持原有样式
                !isMobile && "mt-2 ml-4",
                // 移动端调整位置
                isMobile && "mt-3 ml-2",
              )}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
              transition={{ duration: 0.2, ease: "easeInOut" }}
            >
              <div className="text-brand flex h-full w-full items-center justify-center text-sm opacity-90">
                {feedback.option.text}
              </div>
              <X
                className="cursor-pointer opacity-60"
                size={16}
                onClick={onRemoveFeedback}
              />
            </motion.div>
          )}
          {isEnhanceAnimating && (
            <motion.div
              className="pointer-events-none absolute inset-0 z-20"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="relative h-full w-full">
                {/* Sparkle effect overlay */}
                <motion.div
                  className="absolute inset-0 rounded-[24px] bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-blue-500/10"
                  animate={{
                    background: [
                      "linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1))",
                      "linear-gradient(225deg, rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1))",
                      "linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1))",
                    ],
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                />
                {/* Floating sparkles */}
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute h-2 w-2 rounded-full bg-blue-400"
                    style={{
                      left: `${20 + i * 12}%`,
                      top: `${30 + (i % 2) * 40}%`,
                    }}
                    animate={{
                      y: [-10, -20, -10],
                      opacity: [0, 1, 0],
                      scale: [0.5, 1, 0.5],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      delay: i * 0.2,
                    }}
                  />
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        <MessageInput
          className={cn(
            // 桌面端保持原有样式
            !isMobile && "h-24 px-4 pt-5",
            !isMobile && feedback && "pt-9",
            // 移动端优化样式
            isMobile && "px-4 pt-5",
            isMobile && feedback && "pt-9",
            isEnhanceAnimating && "transition-all duration-500",
          )}
          ref={inputRef}
          onEnter={handleSendMessage}
          onChange={setCurrentPrompt}
        />
      </div>
      <div className={cn(
        "flex items-center py-2",
        // 桌面端保持原有样式
        !isMobile && "px-4",
                 // 移动端优化布局
         isMobile && "px-1 justify-center",
      )}>
        {/* 桌面端显示所有功能，移动端只显示核心功能 */}
        {!isMobile && (
          <div className="flex grow gap-2">
            <Tooltip
              className="max-w-60"
              title={
                <div>
                  <h3 className="mb-2 font-bold">
                    Deep Thinking Mode: {enableDeepThinking ? "On" : "Off"}
                  </h3>
                  <p>
                    When enabled, DeerFlow will use reasoning model{reasoningModel ? ` (${reasoningModel})` : ''} to generate more thoughtful plans.
                  </p>
                </div>
              }
            >
              <Button
                className={cn(
                  "rounded-2xl",
                  enableDeepThinking && "!border-brand !text-brand",
                )}
                variant="outline"
                onClick={() => {
                  setEnableDeepThinking(!enableDeepThinking);
                }}
              >
                <Lightbulb /> Deep Thinking
              </Button>
            </Tooltip>

            <Tooltip
              className="max-w-60"
              title={
                <div>
                  <h3 className="mb-2 font-bold">
                    Investigation Mode: {backgroundInvestigation ? "On" : "Off"}
                  </h3>
                  <p>
                    When enabled, DeerFlow will perform a quick search before
                    planning. This is useful for researches related to ongoing
                    events and news.
                  </p>
                </div>
              }
            >
              <Button
                className={cn(
                  "rounded-2xl",
                  backgroundInvestigation && "!border-brand !text-brand",
                )}
                variant="outline"
                onClick={() =>
                  setEnableBackgroundInvestigation(!backgroundInvestigation)
                }
              >
                <Detective /> Investigation
              </Button>
            </Tooltip>
            <ReportStyleDialog />
          </div>
        )}
        
        {/* 移动端简化的功能按钮 */}
        {isMobile && (
          <div className="flex grow justify-center gap-2">
            <Tooltip
              className="max-w-60"
              title={
                <div>
                  <h3 className="mb-2 font-bold">
                    Investigation Mode: {backgroundInvestigation ? "On" : "Off"}
                  </h3>
                  <p>
                    When enabled, DeerFlow will perform a quick search before
                    planning. This is useful for researches related to ongoing
                    events and news.
                  </p>
                </div>
              }
            >
              <Button
                className={cn(
                  "rounded-2xl",
                  backgroundInvestigation && "!border-brand !text-brand",
                )}
                variant="outline"
                size="sm"
                onClick={() =>
                  setEnableBackgroundInvestigation(!backgroundInvestigation)
                }
              >
                <Detective />
              </Button>
            </Tooltip>
          </div>
        )}

        <div className={cn(
          "flex shrink-0 items-center gap-2",
          isMobile && "justify-center",
        )}>
          <Tooltip title="Enhance prompt with AI">
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "hover:bg-accent h-10 w-10",
                isEnhancing && "animate-pulse",
              )}
              onClick={handleEnhancePrompt}
              disabled={isEnhancing || currentPrompt.trim() === ""}
            >
              {isEnhancing ? (
                <div className="flex h-10 w-10 items-center justify-center">
                  <div className="bg-foreground h-3 w-3 animate-bounce rounded-full opacity-70" />
                </div>
              ) : (
                <MagicWandIcon className="text-brand" />
              )}
            </Button>
          </Tooltip>
          <Tooltip title={responding ? "Stop" : "Send"}>
            <Button
              variant="outline"
              size="icon"
              className={cn("h-10 w-10 rounded-full")}
              onClick={() => responding ? onCancel?.() : inputRef.current?.submit()}
            >
              {responding ? (
                <div className="flex h-10 w-10 items-center justify-center">
                  <div className="bg-foreground h-4 w-4 rounded-sm opacity-70" />
                </div>
              ) : (
                <ArrowUp />
              )}
            </Button>
          </Tooltip>
        </div>
      </div>
      {isEnhancing && (
        <>
          <BorderBeam
            duration={5}
            size={250}
            className="from-transparent via-red-500 to-transparent"
          />
          <BorderBeam
            duration={5}
            delay={3}
            size={250}
            className="from-transparent via-blue-500 to-transparent"
          />
        </>
      )}
    </div>
  );
}
