# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.local
.env.development
.env.production

# configuration files with sensitive data
python_backend/conf.yaml

# Python-generated files (global)
__pycache__/
*.py[oc]
dist/
wheels/
*.egg-info
.coverage
.coverage.*

# Python backend specific
python_backend/agent_history.gif
python_backend/static/browser_history/*.gif
python_backend/.venv/
python_backend/venv/
python_backend/.env
python_backend/.idea/
python_backend/.langgraph_api/
python_backend/coverage.xml
python_backend/coverage/

# Virtual environments (global)
.venv/
venv/

# IDE files (global)
.idea/

# vercel
.vercel
.tmp

# typescript
*.tsbuildinfo
next-env.d.ts

Makefile
.wrangler
wrangler.toml
supabase
