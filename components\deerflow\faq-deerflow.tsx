import { SectionHeader } from "./landing/components/section-header";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";

export default function DeerFlowFAQ() {
  return (
    <section className="container flex flex-col items-center justify-center mt-32 mb-12">
      <SectionHeader
        anchor="faq"
        title="DeerFlow FAQ"
        description="Frequently Asked Questions about DeerFlow, the open-source deep research and agent workflow framework."
      />
      <Accordion type="multiple" className="w-full max-w-2xl mx-auto">
        <AccordionItem value="what-is-deerflow">
          <AccordionTrigger>What is DeerFlow?</AccordionTrigger>
          <AccordionContent>
            DeerFlow is an open-source, community-driven framework designed for deep research and efficient workflow automation. DeerFlow combines large language models with advanced tools such as web search, crawling, and Python execution, enabling users to build powerful agent-based applications and research pipelines. DeerFlow is ideal for AI application development, enterprise knowledge management, and intelligent Q&A scenarios.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="supported-models">
          <AccordionTrigger>Which language models does DeerFlow support?</AccordionTrigger>
          <AccordionContent>
            DeerFlow supports most mainstream open-source and commercial large language models, including OpenAI, DeepSeek, Qwen, Llama, and more. You can flexibly integrate and switch between models according to your needs.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="installation">
          <AccordionTrigger>How do I install and deploy DeerFlow?</AccordionTrigger>
          <AccordionContent>
            To install DeerFlow, clone the repository from GitHub, install Python and Node.js dependencies, and configure your environment variables. DeerFlow supports both local and cloud deployment. For detailed steps, please refer to the official documentation and README.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="tech-stack">
          <AccordionTrigger>What is the technology stack of DeerFlow?</AccordionTrigger>
          <AccordionContent>
            DeerFlow's frontend is built with Next.js, React, and shadcn/ui, while the backend is developed in Python. It supports various AI inference frameworks and databases, ensuring flexibility and scalability for different research and production needs.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="customization">
          <AccordionTrigger>How can I customize and extend DeerFlow workflows?</AccordionTrigger>
          <AccordionContent>
            DeerFlow offers both visual and code-based workflow editing. You can create custom nodes, plugins, and agents to meet your unique business requirements. The modular architecture makes it easy to extend and integrate with other tools.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="license">
          <AccordionTrigger>What is the license of DeerFlow? Can I use it commercially?</AccordionTrigger>
          <AccordionContent>
            DeerFlow is released under the MIT License. It is fully open-source and free for both personal and commercial use, including secondary development.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="community">
          <AccordionTrigger>How can I get support or contribute to DeerFlow?</AccordionTrigger>
          <AccordionContent>
            You can get support via GitHub Issues, the official community, or user groups. Contributions of code, documentation, and plugins are welcome. Please see the GitHub repository for contribution guidelines.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="security">
          <AccordionTrigger>How does DeerFlow ensure data security and privacy?</AccordionTrigger>
          <AccordionContent>
            DeerFlow supports private deployment and local data storage, making it suitable for organizations with high security and privacy requirements. You have full control over your data and environment.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="comparison">
          <AccordionTrigger>How does DeerFlow compare to frameworks like LangChain or RAGFlow?</AccordionTrigger>
          <AccordionContent>
            DeerFlow focuses on workflow orchestration and agent collaboration, supporting multi-model and multi-agent teamwork. Compared to frameworks like LangChain or RAGFlow, DeerFlow is more suitable for complex AI application scenarios that require flexible process management and agent cooperation.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="future">
          <AccordionTrigger>What is the future roadmap for DeerFlow?</AccordionTrigger>
          <AccordionContent>
            DeerFlow is under active development and continues to evolve. The project welcomes community contributions and feedback. Future plans include more integrations, enhanced agent capabilities, and improved user experience. Please follow the GitHub repository for updates.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="use-cases">
          <AccordionTrigger>What are the main use cases for DeerFlow?</AccordionTrigger>
          <AccordionContent>
            DeerFlow is widely used for building intelligent research assistants, enterprise knowledge management systems, automated data analysis pipelines, and multi-agent AI applications. With DeerFlow, you can quickly create solutions for document processing, web search, report generation, and more, all powered by the flexibility of the DeerFlow framework.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="best-practices">
          <AccordionTrigger>What are some best practices for using DeerFlow?</AccordionTrigger>
          <AccordionContent>
            To get the most out of DeerFlow, follow modular design principles, leverage DeerFlow's agent orchestration features, and use version control for your workflow configurations. Regularly update your DeerFlow installation to benefit from the latest features and community contributions. Always test your DeerFlow workflows in a development environment before deploying to production.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="performance">
          <AccordionTrigger>How can I optimize the performance of DeerFlow workflows?</AccordionTrigger>
          <AccordionContent>
            For optimal performance, use efficient data sources, minimize unnecessary agent steps, and monitor resource usage within DeerFlow. DeerFlow supports parallel processing and scalable deployment, allowing you to handle large-scale research tasks efficiently. Refer to the DeerFlow documentation for advanced performance tuning tips.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="integration">
          <AccordionTrigger>Can DeerFlow integrate with other AI platforms or tools?</AccordionTrigger>
          <AccordionContent>
            Yes, DeerFlow is designed for easy integration with other AI platforms, APIs, and data sources. You can connect DeerFlow to external databases, cloud services, and third-party APIs to extend its capabilities. Many users combine DeerFlow with popular tools like LangChain, OpenAI, and custom Python scripts for advanced workflows.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="troubleshooting">
          <AccordionTrigger>How do I troubleshoot common errors in DeerFlow?</AccordionTrigger>
          <AccordionContent>
            If you encounter issues with DeerFlow, check the logs for error messages, verify your configuration files, and consult the DeerFlow community for support. The DeerFlow GitHub repository contains a list of common problems and solutions. Keeping your DeerFlow version up to date also helps prevent known issues.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="user-stories">
          <AccordionTrigger>Are there any real-world user stories or case studies for DeerFlow?</AccordionTrigger>
          <AccordionContent>
            Many organizations and developers have successfully implemented DeerFlow for research automation, business intelligence, and AI-driven content creation. User stories highlight how DeerFlow accelerates project delivery, improves data quality, and enables innovative solutions across industries.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="data-sources">
          <AccordionTrigger>What data sources does DeerFlow support?</AccordionTrigger>
          <AccordionContent>
            DeerFlow supports a wide range of data sources, including web pages, databases, APIs, documents, and more. You can easily configure DeerFlow to fetch, process, and analyze data from multiple sources, making it a versatile choice for any research or automation project.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="api-usage">
          <AccordionTrigger>Does DeerFlow provide an API for developers?</AccordionTrigger>
          <AccordionContent>
            Yes, DeerFlow offers a robust API that allows developers to interact with workflows, agents, and data programmatically. The DeerFlow API makes it easy to integrate DeerFlow into your existing systems and automate complex tasks with minimal effort.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="ui-customization">
          <AccordionTrigger>Can I customize the DeerFlow web UI?</AccordionTrigger>
          <AccordionContent>
            The DeerFlow web UI is fully customizable. You can modify themes, layouts, and components to match your branding or user experience requirements. DeerFlow's frontend is built with modern web technologies, making customization straightforward for developers.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="future-trends">
          <AccordionTrigger>What are the future trends for DeerFlow and agent-based research?</AccordionTrigger>
          <AccordionContent>
            The future of DeerFlow includes deeper AI integration, smarter agent collaboration, and broader support for new data sources and platforms. As the DeerFlow community grows, expect more plugins, advanced features, and best practices to emerge, keeping DeerFlow at the forefront of agent-based research and automation.
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </section>
  );
} 
