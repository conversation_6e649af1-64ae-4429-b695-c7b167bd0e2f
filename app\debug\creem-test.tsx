// app/debug/creem-test.tsx
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function CreemTestPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const testCreemConfig = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/creem/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productType: 'ONETIME',
          userEmail: '<EMAIL>',
          userName: 'Test User',
        }),
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ error: error instanceof Error ? error.message : String(error) });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Creem 支付测试</CardTitle>
          <CardDescription>
            测试 Creem 配置和支付功能
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Badge variant="outline">测试环境</Badge>
            <p className="text-sm text-muted-foreground">
              这将创建一个测试支付会话，用于验证 Creem 集成
            </p>
          </div>
          
          <Button 
            onClick={testCreemConfig}
            disabled={loading}
            className="w-full"
          >
            {loading ? '测试中...' : '测试 Creem 支付'}
          </Button>
          
          {result && (
            <div className="mt-4 p-4 bg-muted rounded-lg">
              <h3 className="font-semibold mb-2">测试结果:</h3>
              <pre className="text-sm overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 